__build_info:
  rime_version: 1.13.1
  timestamps:
    default: 1754711178
    default.custom: 1753237411
    wanxiang_mixedcode.custom: 1754711550
    wanxiang_mixedcode.schema: 1754711178
engine:
  filters:
    - uniquifier
  processors:
    - key_binder
    - speller
    - selector
    - navigator
    - express_editor
  segmentors:
    - abc_segmentor
  translators:
    - echo_translator
    - table_translator
key_binder:
  bindings:
    - {accept: "Shift+Tab", send: "Shift+Left", when: composing}
    - {accept: "Alt+Left", send: "Shift+Left", when: composing}
    - {accept: "Alt+Right", send: "Shift+Right", when: composing}
    - {accept: minus, send: Page_Up, when: has_menu}
    - {accept: equal, send: Page_Down, when: has_menu}
    - {accept: "Control+。", toggle: ascii_punct, when: always}
    - {accept: "Control+period", toggle: ascii_punct, when: always}
    - {accept: "Control+Shift+F", toggle: s2s, when: always}
    - {accept: "Control+Shift+F", toggle: s2s, when: always}
    - {accept: "Control+Shift+5", toggle: full_shape, when: always}
    - {accept: "Control+Shift+percent", toggle: full_shape, when: always}
    - {accept: "Control+k", send: "Shift+Delete", when: composing}
    - {accept: KP_0, send: 0, when: composing}
    - {accept: KP_1, send: 1, when: composing}
    - {accept: KP_2, send: 2, when: composing}
    - {accept: KP_3, send: 3, when: composing}
    - {accept: KP_4, send: 4, when: composing}
    - {accept: KP_5, send: 5, when: composing}
    - {accept: KP_6, send: 6, when: composing}
    - {accept: KP_7, send: 7, when: composing}
    - {accept: KP_8, send: 8, when: composing}
    - {accept: KP_9, send: 9, when: composing}
    - {accept: KP_Decimal, send: period, when: composing}
    - {accept: KP_Multiply, send: asterisk, when: composing}
    - {accept: KP_Add, send: plus, when: composing}
    - {accept: KP_Subtract, send: minus, when: composing}
    - {accept: KP_Divide, send: slash, when: composing}
    - {accept: KP_Enter, send: Return, when: composing}
  select_first_character: bracketleft
  select_last_character: bracketright
menu:
  alternative_select_labels:
    - 1
    - 2
    - 3
    - 4
    - 5
    - 6
    - 7
    - 8
    - 9
    - 10
  page_size: 6
schema:
  author: amzxyz
  description: |
    混合编码负责将英文、中英文混合、携带符号的词组等全部统一到这个方案中完成

  name: "万象：英文与混合编码"
  schema_id: wanxiang_mixedcode
  version: LTS
set_shuru_schema:
  - "derive/(?<!\\d)1([1-9])(?!\\d)/'shí'$1/"
  - "derive/([1-9])0000(?!0)/$1'wàn'/"
  - "derive/([1-9])000(?!0)/$1'qiān'/"
  - "derive/([1-9])00(?!0)/$1'bǎi'/"
  - "derive/([2-9])0(?!0)/$1'shí'/"
  - "derive/(?<!\\d)([2-9])([1-9])(?!\\d)/$1'shí'$2/"
  - "derive/1([4-7|9])/$1'teen'/"
  - "derive/11/'eleven'/"
  - "derive/12/'twelve'/"
  - "derive/13/'thirteen'/"
  - "derive/15/'fifteen'/"
  - "derive/18/'eighteen'/"
  - "derive/0/o/"
  - "derive/0/O/"
  - "derive/0/'zero'/"
  - "derive/1/'one'/"
  - "derive/10/'ten'/"
  - "derive/2/'to'/"
  - "derive/2/'two'/"
  - "derive/3/'three'/"
  - "derive/4/'for'/"
  - "derive/4/'four'/"
  - "derive/5/'five'/"
  - "derive/6/'six'/"
  - "derive/7/'seven'/"
  - "derive/8/'eight'/"
  - "derive/9/'nine'/"
  - "derive/10/'shí'/"
  - "derive/0/'líng'/"
  - "derive/1/'yī'/"
  - "derive/2/'èr'/"
  - "derive/3/'sān'/"
  - "derive/4/'sì'/"
  - "derive/5/'wǔ'/"
  - "derive/6/'liù'/"
  - "derive/7/'qī'/"
  - "derive/8/'bā'/"
  - "derive/9/'jiǔ'/"
  - "derive/\\+/'plus'/"
  - "derive/\\./'dot'/"
  - "derive/@/'at'/"
  - "derive/-/'hyphen'/"
  - "derive/#/'hash'/"
  - "derive/#/'number'/"
  - "derive/#/'sharp'/"
  - "derive/♯/'sharp'/"
  - "derive / 'slash'"
  - "derive/&/'and'/"
  - "derive/%/'percent'/"
  - "derive/--/'jiǎn'jiǎn'/"
  - "derive/^(.*)-$/$1'jiǎn/"
  - "derive/-/'/"
  - "derive/_/'/"
  - "derive/\\./'diǎn'/"
  - "derive/\\+/'jiā'/"
  - "derive/·/'/"
  - "derive/#/'jǐng'/"
  - "derive/[@]/'ài'tè'/"
  - "derive/[@]/'quān'a'/"
  - "xform/'+/'/"
  - "xform/^'(.*)$/$1/"
  - "xform/^(.*)'$/$1/"
  - "xlit/āáǎàōóǒòēéěèīíǐìūúǔùǖǘǚǜü/aaaaooooeeeeiiiiuuuuvvvvv/"
  - "xform/'//"
  - "derive/^([nl])ue$/$1ve/"
  - "derive/'([nl])ue$/'$1ve/"
  - "derive/^([jqxy])u/$1v/"
  - "derive/'([jqxy])u/'$1v/"
  - "derive/^.+$/\\L$0/"
  - "erase/.*[^a-zA-Z].+$/"
speller:
  algebra:
    - "derive/(?<!\\d)1([1-9])(?!\\d)/'shí'$1/"
    - "derive/([1-9])0000(?!0)/$1'wàn'/"
    - "derive/([1-9])000(?!0)/$1'qiān'/"
    - "derive/([1-9])00(?!0)/$1'bǎi'/"
    - "derive/([2-9])0(?!0)/$1'shí'/"
    - "derive/(?<!\\d)([2-9])([1-9])(?!\\d)/$1'shí'$2/"
    - "derive/1([4-7|9])/$1'teen'/"
    - "derive/11/'eleven'/"
    - "derive/12/'twelve'/"
    - "derive/13/'thirteen'/"
    - "derive/15/'fifteen'/"
    - "derive/18/'eighteen'/"
    - "derive/0/o/"
    - "derive/0/O/"
    - "derive/0/'zero'/"
    - "derive/1/'one'/"
    - "derive/10/'ten'/"
    - "derive/2/'to'/"
    - "derive/2/'two'/"
    - "derive/3/'three'/"
    - "derive/4/'for'/"
    - "derive/4/'four'/"
    - "derive/5/'five'/"
    - "derive/6/'six'/"
    - "derive/7/'seven'/"
    - "derive/8/'eight'/"
    - "derive/9/'nine'/"
    - "derive/10/'shí'/"
    - "derive/0/'líng'/"
    - "derive/1/'yī'/"
    - "derive/2/'èr'/"
    - "derive/3/'sān'/"
    - "derive/4/'sì'/"
    - "derive/5/'wǔ'/"
    - "derive/6/'liù'/"
    - "derive/7/'qī'/"
    - "derive/8/'bā'/"
    - "derive/9/'jiǔ'/"
    - "derive/\\+/'plus'/"
    - "derive/\\./'dot'/"
    - "derive/@/'at'/"
    - "derive/-/'hyphen'/"
    - "derive/#/'hash'/"
    - "derive/#/'number'/"
    - "derive/#/'sharp'/"
    - "derive/♯/'sharp'/"
    - "derive / 'slash'"
    - "derive/&/'and'/"
    - "derive/%/'percent'/"
    - "derive/--/'jiǎn'jiǎn'/"
    - "derive/^(.*)-$/$1'jiǎn/"
    - "derive/-/'/"
    - "derive/_/'/"
    - "derive/\\./'diǎn'/"
    - "derive/\\+/'jiā'/"
    - "derive/·/'/"
    - "derive/#/'jǐng'/"
    - "derive/[@]/'ài'tè'/"
    - "derive/[@]/'quān'a'/"
    - "xform/'+/'/"
    - "xform/^'(.*)$/$1/"
    - "xform/^(.*)'$/$1/"
    - "xlit/āáǎàōóǒòēéěèīíǐìūúǔùǖǘǚǜü/aaaaooooeeeeiiiiuuuuvvvvv/"
    - "derive/('|^)([jqxy])u(?=^|$|')/$1$2v/"
    - "derive/('|^)([aoe])([ioun])(?=^|$|')/$1$2$2$3/"
    - "xform/('|^)([aoe])(ng)?(?=^|$|')/$1$2$2$3/"
    - "xform/('|^)([qdjlxnm])iu(?=^|$|')/$1$2<q>/"
    - "xform/('|^)((?:<v>|[aoewtpdfghklzvbnm]))ei(?=^|$|')/$1$2<w>/"
    - "xform/('|^)((?:<v>|<i>|<u>|[qrtysdghjklzxcn]))uan(?=^|$|')/$1$2<r>/"
    - "xform/('|^)([qyjlxn])[uv]e(?=^|$|')/$1$2<t>/"
    - "xform/('|^)((?:<v>|<i>|<u>|[qrtysdghjklzxc]))un(?=^|$|')/$1$2<y>/"
    - "xform/^sh/<u>/"
    - "xform/^ch/<i>/"
    - "xform/^zh/<v>/"
    - "xform/'sh/'<u>/"
    - "xform/'ch/'<i>/"
    - "xform/'zh/'<v>/"
    - "xform/('|^)((?:<v>|<i>|<u>|[tsdghklzcn]))uo(?=^|$|')/$1$2<o>/"
    - "xform/('|^)([qtpdjlxbnm])ie(?=^|$|')/$1$2<p>/"
    - "xform/('|^)((?:<v>|<i>|[qrtysdghjklzxcn]))i?ong(?=^|$|')/$1$2<s>/"
    - "xform/('|^)([qtypdjlxnm])ing(?=^|$|')|('|^)((?:<i>|<u>|[gh]))uai(?=^|$|')/$1$2<k>/"
    - "xform/('|^)((?:<i>|<u>|[aoewtpsdghklzcbnm]))ai(?=^|$|')/$1$2<d>/"
    - "xform/('|^)((?:<v>|<i>|<u>|[wrtpsdfghkzcbnm]))en(?=^|$|')/$1$2<f>/"
    - "xform/('|^)((?:<v>|<i>|<u>|[wrtpsdfghklzcbnm]))eng(?=^|$|')/$1$2<g>/"
    - "xform/('|^)((?:<v>|<i>|<u>|[aoeqghjkxbn]))[iu]ang(?=^|$|')/$1$2<l>/"
    - "xform/('|^)((?:<v>|<i>|<u>|[wrtypsdfghklzcbnm]))ang(?=^|$|')/$1$2<h>/"
    - "xform/('|^)([qtpdjlxbnm])ian(?=^|$|')/$1$2<m>/"
    - "xform/('|^)((?:<v>|<i>|<u>|[wrtypsdfghklzcbnm]))an(?=^|$|')/$1$2<j>/"
    - "xform/('|^)((?:<v>|<i>|<u>|[rtypsdfghklzm]))ou(?=^|$|')/$1$2<z>/"
    - "xform/('|^)([tghkljx])[iu]a(?=^|$|')/$1$2<x>/"
    - "xform/('|^)([qtpdjlxbnm])iao(?=^|$|')/$1$2<n>/"
    - "xform/('|^)((?:<v>|<i>|<u>|[rtypsdghklzcbnm]))ao(?=^|$|')/$1$2<c>/"
    - "xform/('|^)((?:<v>|<i>|<u>|[tsdghkzc]))ui(?=^|$|')/$1$2<v>/"
    - "xform/('|^)([qypjlmnbx])in(?=^|$|')/$1$2<b>/"
    - "xform/'|<|>//"
    - "derive/^.+$/\\L$0/"
    - "erase/.*[^a-zA-Z].+$/"
  alphabet: "abcdefghijklmnopqrstuvwxyz;"
  delimiter: " '"
translator:
  dictionary: wanxiang_mixedcode
  enable_user_dict: false
"全拼":
  - "derive/(?<!\\d)1([1-9])(?!\\d)/'shí'$1/"
  - "derive/([1-9])0000(?!0)/$1'wàn'/"
  - "derive/([1-9])000(?!0)/$1'qiān'/"
  - "derive/([1-9])00(?!0)/$1'bǎi'/"
  - "derive/([2-9])0(?!0)/$1'shí'/"
  - "derive/(?<!\\d)([2-9])([1-9])(?!\\d)/$1'shí'$2/"
  - "derive/1([4-7|9])/$1'teen'/"
  - "derive/11/'eleven'/"
  - "derive/12/'twelve'/"
  - "derive/13/'thirteen'/"
  - "derive/15/'fifteen'/"
  - "derive/18/'eighteen'/"
  - "derive/0/o/"
  - "derive/0/O/"
  - "derive/0/'zero'/"
  - "derive/1/'one'/"
  - "derive/10/'ten'/"
  - "derive/2/'to'/"
  - "derive/2/'two'/"
  - "derive/3/'three'/"
  - "derive/4/'for'/"
  - "derive/4/'four'/"
  - "derive/5/'five'/"
  - "derive/6/'six'/"
  - "derive/7/'seven'/"
  - "derive/8/'eight'/"
  - "derive/9/'nine'/"
  - "derive/10/'shí'/"
  - "derive/0/'líng'/"
  - "derive/1/'yī'/"
  - "derive/2/'èr'/"
  - "derive/3/'sān'/"
  - "derive/4/'sì'/"
  - "derive/5/'wǔ'/"
  - "derive/6/'liù'/"
  - "derive/7/'qī'/"
  - "derive/8/'bā'/"
  - "derive/9/'jiǔ'/"
  - "derive/\\+/'plus'/"
  - "derive/\\./'dot'/"
  - "derive/@/'at'/"
  - "derive/-/'hyphen'/"
  - "derive/#/'hash'/"
  - "derive/#/'number'/"
  - "derive/#/'sharp'/"
  - "derive/♯/'sharp'/"
  - "derive / 'slash'"
  - "derive/&/'and'/"
  - "derive/%/'percent'/"
  - "derive/--/'jiǎn'jiǎn'/"
  - "derive/^(.*)-$/$1'jiǎn/"
  - "derive/-/'/"
  - "derive/_/'/"
  - "derive/\\./'diǎn'/"
  - "derive/\\+/'jiā'/"
  - "derive/·/'/"
  - "derive/#/'jǐng'/"
  - "derive/[@]/'ài'tè'/"
  - "derive/[@]/'quān'a'/"
  - "xform/'+/'/"
  - "xform/^'(.*)$/$1/"
  - "xform/^(.*)'$/$1/"
  - "xlit/āáǎàōóǒòēéěèīíǐìūúǔùǖǘǚǜü/aaaaooooeeeeiiiiuuuuvvvvv/"
  - "xform/'//"
  - "derive/^([nl])ue$/$1ve/"
  - "derive/'([nl])ue$/'$1ve/"
  - "derive/^([jqxy])u/$1v/"
  - "derive/'([jqxy])u/'$1v/"
  - "derive/^.+$/\\L$0/"
  - "erase/.*[^a-zA-Z].+$/"
"国标双拼":
  - "derive/(?<!\\d)1([1-9])(?!\\d)/'shí'$1/"
  - "derive/([1-9])0000(?!0)/$1'wàn'/"
  - "derive/([1-9])000(?!0)/$1'qiān'/"
  - "derive/([1-9])00(?!0)/$1'bǎi'/"
  - "derive/([2-9])0(?!0)/$1'shí'/"
  - "derive/(?<!\\d)([2-9])([1-9])(?!\\d)/$1'shí'$2/"
  - "derive/1([4-7|9])/$1'teen'/"
  - "derive/11/'eleven'/"
  - "derive/12/'twelve'/"
  - "derive/13/'thirteen'/"
  - "derive/15/'fifteen'/"
  - "derive/18/'eighteen'/"
  - "derive/0/o/"
  - "derive/0/O/"
  - "derive/0/'zero'/"
  - "derive/1/'one'/"
  - "derive/10/'ten'/"
  - "derive/2/'to'/"
  - "derive/2/'two'/"
  - "derive/3/'three'/"
  - "derive/4/'for'/"
  - "derive/4/'four'/"
  - "derive/5/'five'/"
  - "derive/6/'six'/"
  - "derive/7/'seven'/"
  - "derive/8/'eight'/"
  - "derive/9/'nine'/"
  - "derive/10/'shí'/"
  - "derive/0/'líng'/"
  - "derive/1/'yī'/"
  - "derive/2/'èr'/"
  - "derive/3/'sān'/"
  - "derive/4/'sì'/"
  - "derive/5/'wǔ'/"
  - "derive/6/'liù'/"
  - "derive/7/'qī'/"
  - "derive/8/'bā'/"
  - "derive/9/'jiǔ'/"
  - "derive/\\+/'plus'/"
  - "derive/\\./'dot'/"
  - "derive/@/'at'/"
  - "derive/-/'hyphen'/"
  - "derive/#/'hash'/"
  - "derive/#/'number'/"
  - "derive/#/'sharp'/"
  - "derive/♯/'sharp'/"
  - "derive / 'slash'"
  - "derive/&/'and'/"
  - "derive/%/'percent'/"
  - "derive/--/'jiǎn'jiǎn'/"
  - "derive/^(.*)-$/$1'jiǎn/"
  - "derive/-/'/"
  - "derive/_/'/"
  - "derive/\\./'diǎn'/"
  - "derive/\\+/'jiā'/"
  - "derive/·/'/"
  - "derive/#/'jǐng'/"
  - "derive/[@]/'ài'tè'/"
  - "derive/[@]/'quān'a'/"
  - "xform/'+/'/"
  - "xform/^'(.*)$/$1/"
  - "xform/^(.*)'$/$1/"
  - "xlit/āáǎàōóǒòēéěèīíǐìūúǔùǖǘǚǜü/aaaaooooeeeeiiiiuuuuvvvvv/"
  - "derive/^([aoe])([ioun])(?=^|$|')/$1$1$2/"
  - "derive/'([aoe])([ioun])(?=^|$|')/'$1$1$2/"
  - "xform/^([aoe])(ng)?(?=^|$|')/$1$1$2/"
  - "xform/'([aoe])(ng)?(?=^|$|')/'$1$1$2/"
  - "xform/('|^)([qdjlxnm])iu(?=^|$|')/$1$2<y>/"
  - "xform/('|^)((?:<v>|[aoewtpdfghklzvbnm]))ei(?=^|$|')/$1$2<b>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[qrtysdghjklzxcn]))uan(?=^|$|')/$1$2<w>/"
  - "xform/('|^)([qyjlxn])[uv]e(?=^|$|')/$1$2<x>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[qrtysdghjklzxc]))un(?=^|$|')/$1$2<z>/"
  - "xform/^sh/<u>/"
  - "xform/^ch/<i>/"
  - "xform/^zh/<v>/"
  - "xform/'sh/'<u>/"
  - "xform/'ch/'<i>/"
  - "xform/'zh/'<v>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[tsdghklzcn]))uo(?=^|$|')/$1$2<o>/"
  - "xform/('|^)([qtpdjlxbnm])ie(?=^|$|')/$1$2<t>/"
  - "xform/('|^)((?:<v>|<i>|[qrtysdghjklzxcn]))i?ong(?=^|$|')/$1$2<s>/"
  - "xform/('|^)([qtypdjlxnm])ing(?=^|$|')|('|^)((?:<i>|<u>|[gh]))uai(?=^|$|')/$1$2<j>/"
  - "xform/('|^)((?:<i>|<u>|[aoewtpsdghklzcbnm]))ai(?=^|$|')/$1$2<k>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[wrtpsdfghkzcbnm]))en(?=^|$|')/$1$2<r>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[wrtpsdfghklzcbnm]))eng(?=^|$|')/$1$2<h>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[aoeqghjkxbn]))[iu]ang(?=^|$|')/$1$2<n>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[wrtypsdfghklzcbnm]))ang(?=^|$|')/$1$2<g>/"
  - "xform/('|^)([qtpdjlxbnm])ian(?=^|$|')/$1$2<d>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[wrtypsdfghklzcbnm]))an(?=^|$|')/$1$2<f>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[rtypsdfghklzm]))ou(?=^|$|')/$1$2<p>/"
  - "xform/('|^)([tghkljx])[iu]a(?=^|$|')/$1$2<q>/"
  - "xform/('|^)([qtpdjlxbnm])iao(?=^|$|')/$1$2<m>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[rtypsdghklzcbnm]))ao(?=^|$|')/$1$2<c>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[tsdghkzc]))ui(?=^|$|')/$1$2<v>/"
  - "xform/('|^)([qypjlmnbx])in(?=^|$|')/$1$2<l>/"
  - "xform/'|<|>//"
  - "derive/^.+$/\\L$0/"
  - "erase/.*[^a-zA-Z].+$/"
"小鹤双拼":
  - "derive/(?<!\\d)1([1-9])(?!\\d)/'shí'$1/"
  - "derive/([1-9])0000(?!0)/$1'wàn'/"
  - "derive/([1-9])000(?!0)/$1'qiān'/"
  - "derive/([1-9])00(?!0)/$1'bǎi'/"
  - "derive/([2-9])0(?!0)/$1'shí'/"
  - "derive/(?<!\\d)([2-9])([1-9])(?!\\d)/$1'shí'$2/"
  - "derive/1([4-7|9])/$1'teen'/"
  - "derive/11/'eleven'/"
  - "derive/12/'twelve'/"
  - "derive/13/'thirteen'/"
  - "derive/15/'fifteen'/"
  - "derive/18/'eighteen'/"
  - "derive/0/o/"
  - "derive/0/O/"
  - "derive/0/'zero'/"
  - "derive/1/'one'/"
  - "derive/10/'ten'/"
  - "derive/2/'to'/"
  - "derive/2/'two'/"
  - "derive/3/'three'/"
  - "derive/4/'for'/"
  - "derive/4/'four'/"
  - "derive/5/'five'/"
  - "derive/6/'six'/"
  - "derive/7/'seven'/"
  - "derive/8/'eight'/"
  - "derive/9/'nine'/"
  - "derive/10/'shí'/"
  - "derive/0/'líng'/"
  - "derive/1/'yī'/"
  - "derive/2/'èr'/"
  - "derive/3/'sān'/"
  - "derive/4/'sì'/"
  - "derive/5/'wǔ'/"
  - "derive/6/'liù'/"
  - "derive/7/'qī'/"
  - "derive/8/'bā'/"
  - "derive/9/'jiǔ'/"
  - "derive/\\+/'plus'/"
  - "derive/\\./'dot'/"
  - "derive/@/'at'/"
  - "derive/-/'hyphen'/"
  - "derive/#/'hash'/"
  - "derive/#/'number'/"
  - "derive/#/'sharp'/"
  - "derive/♯/'sharp'/"
  - "derive / 'slash'"
  - "derive/&/'and'/"
  - "derive/%/'percent'/"
  - "derive/--/'jiǎn'jiǎn'/"
  - "derive/^(.*)-$/$1'jiǎn/"
  - "derive/-/'/"
  - "derive/_/'/"
  - "derive/\\./'diǎn'/"
  - "derive/\\+/'jiā'/"
  - "derive/·/'/"
  - "derive/#/'jǐng'/"
  - "derive/[@]/'ài'tè'/"
  - "derive/[@]/'quān'a'/"
  - "xform/'+/'/"
  - "xform/^'(.*)$/$1/"
  - "xform/^(.*)'$/$1/"
  - "xlit/āáǎàōóǒòēéěèīíǐìūúǔùǖǘǚǜü/aaaaooooeeeeiiiiuuuuvvvvv/"
  - "derive/('|^)([jqxy])u(?=^|$|')/$1$2v/"
  - "derive/('|^)([aoe])([ioun])(?=^|$|')/$1$2$2$3/"
  - "xform/('|^)([aoe])(ng)?(?=^|$|')/$1$2$2$3/"
  - "xform/('|^)([qdjlxnm])iu(?=^|$|')/$1$2<q>/"
  - "xform/('|^)((?:<v>|[aoewtpdfghklzvbnm]))ei(?=^|$|')/$1$2<w>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[qrtysdghjklzxcn]))uan(?=^|$|')/$1$2<r>/"
  - "xform/('|^)([qyjlxn])[uv]e(?=^|$|')/$1$2<t>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[qrtysdghjklzxc]))un(?=^|$|')/$1$2<y>/"
  - "xform/^sh/<u>/"
  - "xform/^ch/<i>/"
  - "xform/^zh/<v>/"
  - "xform/'sh/'<u>/"
  - "xform/'ch/'<i>/"
  - "xform/'zh/'<v>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[tsdghklzcn]))uo(?=^|$|')/$1$2<o>/"
  - "xform/('|^)([qtpdjlxbnm])ie(?=^|$|')/$1$2<p>/"
  - "xform/('|^)((?:<v>|<i>|[qrtysdghjklzxcn]))i?ong(?=^|$|')/$1$2<s>/"
  - "xform/('|^)([qtypdjlxnm])ing(?=^|$|')|('|^)((?:<i>|<u>|[gh]))uai(?=^|$|')/$1$2<k>/"
  - "xform/('|^)((?:<i>|<u>|[aoewtpsdghklzcbnm]))ai(?=^|$|')/$1$2<d>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[wrtpsdfghkzcbnm]))en(?=^|$|')/$1$2<f>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[wrtpsdfghklzcbnm]))eng(?=^|$|')/$1$2<g>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[aoeqghjkxbn]))[iu]ang(?=^|$|')/$1$2<l>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[wrtypsdfghklzcbnm]))ang(?=^|$|')/$1$2<h>/"
  - "xform/('|^)([qtpdjlxbnm])ian(?=^|$|')/$1$2<m>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[wrtypsdfghklzcbnm]))an(?=^|$|')/$1$2<j>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[rtypsdfghklzm]))ou(?=^|$|')/$1$2<z>/"
  - "xform/('|^)([tghkljx])[iu]a(?=^|$|')/$1$2<x>/"
  - "xform/('|^)([qtpdjlxbnm])iao(?=^|$|')/$1$2<n>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[rtypsdghklzcbnm]))ao(?=^|$|')/$1$2<c>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[tsdghkzc]))ui(?=^|$|')/$1$2<v>/"
  - "xform/('|^)([qypjlmnbx])in(?=^|$|')/$1$2<b>/"
  - "xform/'|<|>//"
  - "derive/^.+$/\\L$0/"
  - "erase/.*[^a-zA-Z].+$/"
"微软双拼":
  - "derive/(?<!\\d)1([1-9])(?!\\d)/'shí'$1/"
  - "derive/([1-9])0000(?!0)/$1'wàn'/"
  - "derive/([1-9])000(?!0)/$1'qiān'/"
  - "derive/([1-9])00(?!0)/$1'bǎi'/"
  - "derive/([2-9])0(?!0)/$1'shí'/"
  - "derive/(?<!\\d)([2-9])([1-9])(?!\\d)/$1'shí'$2/"
  - "derive/1([4-7|9])/$1'teen'/"
  - "derive/11/'eleven'/"
  - "derive/12/'twelve'/"
  - "derive/13/'thirteen'/"
  - "derive/15/'fifteen'/"
  - "derive/18/'eighteen'/"
  - "derive/0/o/"
  - "derive/0/O/"
  - "derive/0/'zero'/"
  - "derive/1/'one'/"
  - "derive/10/'ten'/"
  - "derive/2/'to'/"
  - "derive/2/'two'/"
  - "derive/3/'three'/"
  - "derive/4/'for'/"
  - "derive/4/'four'/"
  - "derive/5/'five'/"
  - "derive/6/'six'/"
  - "derive/7/'seven'/"
  - "derive/8/'eight'/"
  - "derive/9/'nine'/"
  - "derive/10/'shí'/"
  - "derive/0/'líng'/"
  - "derive/1/'yī'/"
  - "derive/2/'èr'/"
  - "derive/3/'sān'/"
  - "derive/4/'sì'/"
  - "derive/5/'wǔ'/"
  - "derive/6/'liù'/"
  - "derive/7/'qī'/"
  - "derive/8/'bā'/"
  - "derive/9/'jiǔ'/"
  - "derive/\\+/'plus'/"
  - "derive/\\./'dot'/"
  - "derive/@/'at'/"
  - "derive/-/'hyphen'/"
  - "derive/#/'hash'/"
  - "derive/#/'number'/"
  - "derive/#/'sharp'/"
  - "derive/♯/'sharp'/"
  - "derive / 'slash'"
  - "derive/&/'and'/"
  - "derive/%/'percent'/"
  - "derive/--/'jiǎn'jiǎn'/"
  - "derive/^(.*)-$/$1'jiǎn/"
  - "derive/-/'/"
  - "derive/_/'/"
  - "derive/\\./'diǎn'/"
  - "derive/\\+/'jiā'/"
  - "derive/·/'/"
  - "derive/#/'jǐng'/"
  - "derive/[@]/'ài'tè'/"
  - "derive/[@]/'quān'a'/"
  - "xform/'+/'/"
  - "xform/^'(.*)$/$1/"
  - "xform/^(.*)'$/$1/"
  - "xlit/āáǎàōóǒòēéěèīíǐìūúǔùǖǘǚǜü/aaaaooooeeeeiiiiuuuuvvvvv/"
  - "derive/('|^)([jqxy])u(?=^|$|')/$1$2v/"
  - "derive/('|^)([aoe].*)(?=^|$|')/$1o$2/"
  - "xform/('|^)([ae])(.*)(?=^|$|')/$1$2$2$3/"
  - "xform/('|^)([qdjlxnm])iu(?=^|$|')/$1$2<q>/"
  - "xform/('|^)([tghkljx])[iu]a(?=^|$|')/$1$2<w>/"
  - "xform/('|^)er(?=^|$|')|('|^)((?:<v>|<i>|<u>|[qrtysdghjklzxcn]))[uv]an(?=^|$|')/$1$2<r>/"
  - "xform/('|^)([qyjlxn])[uv]e(?=^|$|')/$1$2<t>/"
  - "xform/('|^)([yjlx])v(?=^|$|')|('|^)((?:<i>|<u>|[gh]))uai(?=^|$|')/$1$2<y>/"
  - "xform/^sh/<u>/"
  - "xform/^ch/<i>/"
  - "xform/^zh/<v>/"
  - "xform/'sh/'<u>/"
  - "xform/'ch/'<i>/"
  - "xform/'zh/'<v>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[tsdghklzcn]))uo(?=^|$|')/$1$2<o>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[qrtysdghjklzxc]))[uv]n(?=^|$|')/$1$2<p>/"
  - "xform/('|^)((?:<v>|<i>|[qrtysdghjklzxcn]))i?ong(?=^|$|')/$1$2<s>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[aoeqghjkxbn]))[iu]ang(?=^|$|')/$1$2<d>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[wrtpsdfghkzcbnm]))en(?=^|$|')/$1$2<f>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[wrtpsdfghklzcbnm]))eng(?=^|$|')/$1$2<g>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[wrtypsdfghklzcbnm]))ang(?=^|$|')/$1$2<h>/"
  - "xform/('|^)([qtpdjlxbnm])ian(?=^|$|')/$1$2<m>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[wrtypsdfghklzcbnm]))an(?=^|$|')/$1$2<j>/"
  - "xform/('|^)([qtpdjlxbnm])iao(?=^|$|')/$1$2<c>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[rtypsdghklzcbnm]))ao(?=^|$|')/$1$2<k>/"
  - "xform/('|^)((?:<i>|<u>|[aoewtpsdghklzcbnm]))ai(?=^|$|')/$1$2<l>/"
  - "xform/('|^)((?:<v>|[aoewtpdfghklzvbnm]))ei(?=^|$|')/$1$2<z>/"
  - "xform/('|^)([qtpdjlxbnm])ie(?=^|$|')/$1$2<x>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[tsdghkzc]))ui(?=^|$|')/$1$2<v>/"
  - "derive/<t>(?=^|$|')/<v>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[rtypsdfghklzm]))ou(?=^|$|')/$1$2<b>/"
  - "xform/('|^)([qypjlmnbx])in(?=^|$|')/$1$2<n>/"
  - "xform/('|^)([qtypdjlxnm])ing(?=^|$|')/$1$2;/"
  - "xform/'|<|>//"
  - "derive/^.+$/\\L$0/"
  - "erase/.*[^a-zA-Z].+$/"
"拼音加加":
  - "derive/(?<!\\d)1([1-9])(?!\\d)/'shí'$1/"
  - "derive/([1-9])0000(?!0)/$1'wàn'/"
  - "derive/([1-9])000(?!0)/$1'qiān'/"
  - "derive/([1-9])00(?!0)/$1'bǎi'/"
  - "derive/([2-9])0(?!0)/$1'shí'/"
  - "derive/(?<!\\d)([2-9])([1-9])(?!\\d)/$1'shí'$2/"
  - "derive/1([4-7|9])/$1'teen'/"
  - "derive/11/'eleven'/"
  - "derive/12/'twelve'/"
  - "derive/13/'thirteen'/"
  - "derive/15/'fifteen'/"
  - "derive/18/'eighteen'/"
  - "derive/0/o/"
  - "derive/0/O/"
  - "derive/0/'zero'/"
  - "derive/1/'one'/"
  - "derive/10/'ten'/"
  - "derive/2/'to'/"
  - "derive/2/'two'/"
  - "derive/3/'three'/"
  - "derive/4/'for'/"
  - "derive/4/'four'/"
  - "derive/5/'five'/"
  - "derive/6/'six'/"
  - "derive/7/'seven'/"
  - "derive/8/'eight'/"
  - "derive/9/'nine'/"
  - "derive/10/'shí'/"
  - "derive/0/'líng'/"
  - "derive/1/'yī'/"
  - "derive/2/'èr'/"
  - "derive/3/'sān'/"
  - "derive/4/'sì'/"
  - "derive/5/'wǔ'/"
  - "derive/6/'liù'/"
  - "derive/7/'qī'/"
  - "derive/8/'bā'/"
  - "derive/9/'jiǔ'/"
  - "derive/\\+/'plus'/"
  - "derive/\\./'dot'/"
  - "derive/@/'at'/"
  - "derive/-/'hyphen'/"
  - "derive/#/'hash'/"
  - "derive/#/'number'/"
  - "derive/#/'sharp'/"
  - "derive/♯/'sharp'/"
  - "derive / 'slash'"
  - "derive/&/'and'/"
  - "derive/%/'percent'/"
  - "derive/--/'jiǎn'jiǎn'/"
  - "derive/^(.*)-$/$1'jiǎn/"
  - "derive/-/'/"
  - "derive/_/'/"
  - "derive/\\./'diǎn'/"
  - "derive/\\+/'jiā'/"
  - "derive/·/'/"
  - "derive/#/'jǐng'/"
  - "derive/[@]/'ài'tè'/"
  - "derive/[@]/'quān'a'/"
  - "xform/'+/'/"
  - "xform/^'(.*)$/$1/"
  - "xform/^(.*)'$/$1/"
  - "xlit/āáǎàōóǒòēéěèīíǐìūúǔùǖǘǚǜü/aaaaooooeeeeiiiiuuuuvvvvv/"
  - "derive/('|^)([jqxy])u(?=^|$|')/$1$2v/"
  - "derive/('|^)([aoe])([ioun])(?=^|$|')/$1$2$2$3/"
  - "xform/('|^)([aoe])(ng)?(?=^|$|')/$1$2$2$3/"
  - "xform/('|^)([qdjlxnm])iu(?=^|$|')/$1$2<n>/"
  - "xform/('|^)([tghkljx])[iu]a(?=^|$|')/$1$2<b>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[qrtysdghjklzxcn]))[uv]an(?=^|$|')/$1$2<c>/"
  - "xform/('|^)((?:<i>|<u>|[gh]))uai(?=^|$|')|('|^)([qyjlxn])[uv]e(?=^|$|')/$1$2<x>/"
  - "xform/('|^)([qtypdjlxnm])ing(?=^|$|')|('|^)er(?=^|$|')/$1$2<q>/"
  - "xform/^sh/<i>/"
  - "xform/^ch/<u>/"
  - "xform/^zh/<v>/"
  - "xform/'sh/'<i>/"
  - "xform/'ch/'<u>/"
  - "xform/'zh/'<v>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[tsdghklzcn]))uo(?=^|$|')/$1$2<o>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[qrtysdghjklzxc]))[uv]n(?=^|$|')/$1$2<z>/"
  - "xform/('|^)((?:<v>|<i>|[qrtysdghjklzxcn]))i?ong(?=^|$|')/$1$2<y>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[aoeqghjkxbn]))[iu]ang(?=^|$|')/$1$2<h>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[wrtpsdfghkzcbnm]))en(?=^|$|')/$1$2<r>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[wrtpsdfghklzcbnm]))eng(?=^|$|')/$1$2<t>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[wrtypsdfghklzcbnm]))ang(?=^|$|')/$1$2<g>/"
  - "xform/('|^)([qtpdjlxbnm])ian(?=^|$|')/$1$2<j>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[wrtypsdfghklzcbnm]))an(?=^|$|')/$1$2<f>/"
  - "xform/('|^)([qtpdjlxbnm])iao(?=^|$|')/$1$2<k>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[rtypsdghklzcbnm]))ao(?=^|$|')/$1$2<d>/"
  - "xform/('|^)((?:<i>|<u>|[aoewtpsdghklzcbnm]))ai(?=^|$|')/$1$2<s>/"
  - "xform/('|^)((?:<v>|[aoewtpdfghklzvbnm]))ei(?=^|$|')/$1$2<w>/"
  - "xform/('|^)([qtpdjlxbnm])ie(?=^|$|')/$1$2<m>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[tsdghkzc]))ui(?=^|$|')/$1$2<v>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[rtypsdfghklzm]))ou(?=^|$|')/$1$2<p>/"
  - "xform/('|^)([qypjlmnbx])in(?=^|$|')/$1$2<l>/"
  - "xform/'|<|>//"
  - "derive/^.+$/\\L$0/"
  - "erase/.*[^a-zA-Z].+$/"
"搜狗双拼":
  - "derive/(?<!\\d)1([1-9])(?!\\d)/'shí'$1/"
  - "derive/([1-9])0000(?!0)/$1'wàn'/"
  - "derive/([1-9])000(?!0)/$1'qiān'/"
  - "derive/([1-9])00(?!0)/$1'bǎi'/"
  - "derive/([2-9])0(?!0)/$1'shí'/"
  - "derive/(?<!\\d)([2-9])([1-9])(?!\\d)/$1'shí'$2/"
  - "derive/1([4-7|9])/$1'teen'/"
  - "derive/11/'eleven'/"
  - "derive/12/'twelve'/"
  - "derive/13/'thirteen'/"
  - "derive/15/'fifteen'/"
  - "derive/18/'eighteen'/"
  - "derive/0/o/"
  - "derive/0/O/"
  - "derive/0/'zero'/"
  - "derive/1/'one'/"
  - "derive/10/'ten'/"
  - "derive/2/'to'/"
  - "derive/2/'two'/"
  - "derive/3/'three'/"
  - "derive/4/'for'/"
  - "derive/4/'four'/"
  - "derive/5/'five'/"
  - "derive/6/'six'/"
  - "derive/7/'seven'/"
  - "derive/8/'eight'/"
  - "derive/9/'nine'/"
  - "derive/10/'shí'/"
  - "derive/0/'líng'/"
  - "derive/1/'yī'/"
  - "derive/2/'èr'/"
  - "derive/3/'sān'/"
  - "derive/4/'sì'/"
  - "derive/5/'wǔ'/"
  - "derive/6/'liù'/"
  - "derive/7/'qī'/"
  - "derive/8/'bā'/"
  - "derive/9/'jiǔ'/"
  - "derive/\\+/'plus'/"
  - "derive/\\./'dot'/"
  - "derive/@/'at'/"
  - "derive/-/'hyphen'/"
  - "derive/#/'hash'/"
  - "derive/#/'number'/"
  - "derive/#/'sharp'/"
  - "derive/♯/'sharp'/"
  - "derive / 'slash'"
  - "derive/&/'and'/"
  - "derive/%/'percent'/"
  - "derive/--/'jiǎn'jiǎn'/"
  - "derive/^(.*)-$/$1'jiǎn/"
  - "derive/-/'/"
  - "derive/_/'/"
  - "derive/\\./'diǎn'/"
  - "derive/\\+/'jiā'/"
  - "derive/·/'/"
  - "derive/#/'jǐng'/"
  - "derive/[@]/'ài'tè'/"
  - "derive/[@]/'quān'a'/"
  - "xform/'+/'/"
  - "xform/^'(.*)$/$1/"
  - "xform/^(.*)'$/$1/"
  - "xlit/āáǎàōóǒòēéěèīíǐìūúǔùǖǘǚǜü/aaaaooooeeeeiiiiuuuuvvvvv/"
  - "derive/('|^)([jqxy])u(?=^|$|')/$1$2v/"
  - "derive/('|^)([aoe].*)(?=^|$|')/$1o$2/"
  - "xform/('|^)([ae])(.*)(?=^|$|')/'$1$2$2$3/"
  - "xform/('|^)([qdjlxnm])iu(?=^|$|')/$1$2<q>/"
  - "xform/('|^)([tghkljx])[iu]a(?=^|$|')/<w>/"
  - "xform/('|^)er(?=^|$|')/$1<r>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[qrtysdghjklzxcn]))[uv]an(?=^|$|')/$1$2<r>/"
  - "xform/('|^)([qyjlxn])[uv]e(?=^|$|')/$1$2<t>/"
  - "xform/('|^)([yjlx])v(?=^|$|')|('|^)((?:<i>|<u>|[gh]))uai(?=^|$|')/$1$2<y>/"
  - "xform/^sh/<u>/"
  - "xform/^ch/<i>/"
  - "xform/^zh/<v>/"
  - "xform/'sh/'<u>/"
  - "xform/'ch/'<i>/"
  - "xform/'zh/'<v>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[tsdghklzcn]))uo(?=^|$|')/$1$2<o>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[qrtysdghjklzxc]))[uv]n(?=^|$|')/$1$2<p>/"
  - "xform/('|^)((?:<v>|<i>|[qrtysdghjklzxcn]))i?ong(?=^|$|')/$1$2<s>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[aoeqghjkxbn]))[iu]ang(?=^|$|')/$1$2<d>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[wrtpsdfghkzcbnm]))en(?=^|$|')/$1$2<f>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[wrtpsdfghklzcbnm]))eng(?=^|$|')/$1$2<g>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[wrtypsdfghklzcbnm]))ang(?=^|$|')/$1$2<h>/"
  - "xform/('|^)([qtpdjlxbnm])ian(?=^|$|')/$1$2<m>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[wrtypsdfghklzcbnm]))an(?=^|$|')/$1$2<j>/"
  - "xform/('|^)([qtpdjlxbnm])iao(?=^|$|')/$1$2<c>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[rtypsdghklzcbnm]))ao(?=^|$|')/$1$2<k>/"
  - "xform/('|^)((?:<i>|<u>|[aoewtpsdghklzcbnm]))ai(?=^|$|')/$1$2<l>/"
  - "xform/('|^)((?:<v>|[aoewtpdfghklzvbnm]))ei(?=^|$|')/$1$2<z>/"
  - "xform/('|^)([qtpdjlxbnm])ie(?=^|$|')/$1$2<x>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[tsdghkzc]))ui(?=^|$|')/$1$2<v>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[rtypsdfghklzm]))ou(?=^|$|')/$1$2<b>/"
  - "xform/('|^)([qypjlmnbx])in(?=^|$|')/$1$2<n>/"
  - "xform/('|^)([qtypdjlxnm])ing(?=^|$|')/$1$2;/"
  - "xform/'|<|>//"
  - "derive/^.+$/\\L$0/"
  - "erase/.*[^a-zA-Z].+$/"
"智能ABC":
  - "derive/(?<!\\d)1([1-9])(?!\\d)/'shí'$1/"
  - "derive/([1-9])0000(?!0)/$1'wàn'/"
  - "derive/([1-9])000(?!0)/$1'qiān'/"
  - "derive/([1-9])00(?!0)/$1'bǎi'/"
  - "derive/([2-9])0(?!0)/$1'shí'/"
  - "derive/(?<!\\d)([2-9])([1-9])(?!\\d)/$1'shí'$2/"
  - "derive/1([4-7|9])/$1'teen'/"
  - "derive/11/'eleven'/"
  - "derive/12/'twelve'/"
  - "derive/13/'thirteen'/"
  - "derive/15/'fifteen'/"
  - "derive/18/'eighteen'/"
  - "derive/0/o/"
  - "derive/0/O/"
  - "derive/0/'zero'/"
  - "derive/1/'one'/"
  - "derive/10/'ten'/"
  - "derive/2/'to'/"
  - "derive/2/'two'/"
  - "derive/3/'three'/"
  - "derive/4/'for'/"
  - "derive/4/'four'/"
  - "derive/5/'five'/"
  - "derive/6/'six'/"
  - "derive/7/'seven'/"
  - "derive/8/'eight'/"
  - "derive/9/'nine'/"
  - "derive/10/'shí'/"
  - "derive/0/'líng'/"
  - "derive/1/'yī'/"
  - "derive/2/'èr'/"
  - "derive/3/'sān'/"
  - "derive/4/'sì'/"
  - "derive/5/'wǔ'/"
  - "derive/6/'liù'/"
  - "derive/7/'qī'/"
  - "derive/8/'bā'/"
  - "derive/9/'jiǔ'/"
  - "derive/\\+/'plus'/"
  - "derive/\\./'dot'/"
  - "derive/@/'at'/"
  - "derive/-/'hyphen'/"
  - "derive/#/'hash'/"
  - "derive/#/'number'/"
  - "derive/#/'sharp'/"
  - "derive/♯/'sharp'/"
  - "derive / 'slash'"
  - "derive/&/'and'/"
  - "derive/%/'percent'/"
  - "derive/--/'jiǎn'jiǎn'/"
  - "derive/^(.*)-$/$1'jiǎn/"
  - "derive/-/'/"
  - "derive/_/'/"
  - "derive/\\./'diǎn'/"
  - "derive/\\+/'jiā'/"
  - "derive/·/'/"
  - "derive/#/'jǐng'/"
  - "derive/[@]/'ài'tè'/"
  - "derive/[@]/'quān'a'/"
  - "xform/'+/'/"
  - "xform/^'(.*)$/$1/"
  - "xform/^(.*)'$/$1/"
  - "xlit/āáǎàōóǒòēéěèīíǐìūúǔùǖǘǚǜü/aaaaooooeeeeiiiiuuuuvvvvv/"
  - "xform/^zh/<a>/"
  - "xform/^ch/<e>/"
  - "xform/^sh/<v>/"
  - "xform/'zh/'<a>/"
  - "xform/'ch/'<e>/"
  - "xform/'sh/'<v>/"
  - "xform/('|^)([aoe].*)(?=^|$|')/$1<o>$2/"
  - "xform/('|^)((?:<v>|[aoewtpdfghklzvbnm]))ei(?=^|$|')/$1$2<q>/"
  - "xform/('|^)([qtpdjlxbnm])ian(?=^|$|')/$1$2<w>/"
  - "xform/('|^)er(?=^|$|')/$1<r>/"
  - "xform/('|^)([qdjlxnm])iu(?=^|$|')/$1$2<r>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[aoeqghjkxbn]))[iu]ang(?=^|$|')/$1$2<t>/"
  - "xform/('|^)([qtypdjlxnm])ing(?=^|$|')/$1$2<y>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[tsdghklzcn]))uo(?=^|$|')/$1$2<o>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[qrtysdghjklzxcn]))uan(?=^|$|')/$1$2<p>/"
  - "xform/('|^)((?:<v>|<i>|[qrtysdghjklzxcn]))i?ong(?=^|$|')/$1$2<s>/"
  - "xform/('|^)([tghkljx])[iu]a(?=^|$|')/$1$2<d>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[wrtpsdfghkzcbnm]))en(?=^|$|')/$1$2<f>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[wrtpsdfghklzcbnm]))eng(?=^|$|')/$1$2<g>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[wrtypsdfghklzcbnm]))ang(?=^|$|')/$1$2<h>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[wrtypsdfghklzcbnm]))an(?=^|$|')/$1$2<j>/"
  - "xform/('|^)([qtpdjlxbnm])iao(?=^|$|')/$1$2<z>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[rtypsdghklzcbnm]))ao(?=^|$|')/$1$2<k>/"
  - "xform/('|^)([qypjlmnbx])in(?=^|$|')|('|^)((?:<i>|<u>|[gh]))uai(?=^|$|')/$1$2<c>/"
  - "xform/('|^)((?:<i>|<u>|[aoewtpsdghklzcbnm]))ai(?=^|$|')/$1$2<l>/"
  - "xform/('|^)([qtpdjlxbnm])ie(?=^|$|')/$1$2<x>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[rtypsdfghklzm]))ou(?=^|$|')/$1$2<b>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[qrtysdghjklzxc]))un(?=^|$|')/$1$2<n>/"
  - "xform/('|^)([qyjlxn])[uv]e(?=^|$|')|('|^)((?:<v>|<i>|<u>|[tsdghkzc]))ui(?=^|$|')/$1$2<m>/"
  - "xform/'|<|>//"
  - "derive/^.+$/\\L$0/"
  - "erase/.*[^a-zA-Z].+$/"
"汉心龙":
  - "derive/(?<!\\d)1([1-9])(?!\\d)/'shí'$1/"
  - "derive/([1-9])0000(?!0)/$1'wàn'/"
  - "derive/([1-9])000(?!0)/$1'qiān'/"
  - "derive/([1-9])00(?!0)/$1'bǎi'/"
  - "derive/([2-9])0(?!0)/$1'shí'/"
  - "derive/(?<!\\d)([2-9])([1-9])(?!\\d)/$1'shí'$2/"
  - "derive/1([4-7|9])/$1'teen'/"
  - "derive/11/'eleven'/"
  - "derive/12/'twelve'/"
  - "derive/13/'thirteen'/"
  - "derive/15/'fifteen'/"
  - "derive/18/'eighteen'/"
  - "derive/0/o/"
  - "derive/0/O/"
  - "derive/0/'zero'/"
  - "derive/1/'one'/"
  - "derive/10/'ten'/"
  - "derive/2/'to'/"
  - "derive/2/'two'/"
  - "derive/3/'three'/"
  - "derive/4/'for'/"
  - "derive/4/'four'/"
  - "derive/5/'five'/"
  - "derive/6/'six'/"
  - "derive/7/'seven'/"
  - "derive/8/'eight'/"
  - "derive/9/'nine'/"
  - "derive/10/'shí'/"
  - "derive/0/'líng'/"
  - "derive/1/'yī'/"
  - "derive/2/'èr'/"
  - "derive/3/'sān'/"
  - "derive/4/'sì'/"
  - "derive/5/'wǔ'/"
  - "derive/6/'liù'/"
  - "derive/7/'qī'/"
  - "derive/8/'bā'/"
  - "derive/9/'jiǔ'/"
  - "derive/\\+/'plus'/"
  - "derive/\\./'dot'/"
  - "derive/@/'at'/"
  - "derive/-/'hyphen'/"
  - "derive/#/'hash'/"
  - "derive/#/'number'/"
  - "derive/#/'sharp'/"
  - "derive/♯/'sharp'/"
  - "derive / 'slash'"
  - "derive/&/'and'/"
  - "derive/%/'percent'/"
  - "derive/--/'jiǎn'jiǎn'/"
  - "derive/^(.*)-$/$1'jiǎn/"
  - "derive/-/'/"
  - "derive/_/'/"
  - "derive/\\./'diǎn'/"
  - "derive/\\+/'jiā'/"
  - "derive/·/'/"
  - "derive/#/'jǐng'/"
  - "derive/[@]/'ài'tè'/"
  - "derive/[@]/'quān'a'/"
  - "xform/'+/'/"
  - "xform/^'(.*)$/$1/"
  - "xform/^(.*)'$/$1/"
  - "xform/('|^)(ā|á|ǎ|à)([ioun])(?=^|$|')/'<q>$2$3/"
  - "xform/('|^)(ō|ó|ǒ|ò)([ioun])(?=^|$|')/'<r>$2$3/"
  - "xform/('|^)(ē|é|ě|è)([iounr])(?=^|$|')/'<b>$2$3/"
  - "xform/('|^)(ā|á|ǎ|à)(ng)(?=^|$|')/'<q>$2$3/"
  - "xform/('|^)(ō|ó|ǒ|ò)(ng)(?=^|$|')/'<r>$2$3/"
  - "xform/('|^)(ē|é|ě|è)(ng)(?=^|$|')/'<b>$2$3/"
  - "xform/('|^)(a|ā|á|ǎ|à)(?=^|$|')/'<q>$2/"
  - "xform/('|^)(o|ō|ó|ǒ|ò)(?=^|$|')/'<r>$2/"
  - "xform/('|^)(e|ē|é|ě|è)(?=^|$|')/'<b>$2/"
  - "xform/(')([jqxy])u(?=^|$|')/'$2ü/"
  - "xform/('|^)([jqxy])ū(?=^|$|')/'$2ǖ/"
  - "xform/('|^)([jqxy])ú(?=^|$|')/'$2ǘ/"
  - "xform/('|^)([jqxy])ǔ(?=^|$|')/'$2ǚ/"
  - "xform/('|^)([jqxy])ù(?=^|$|')/'$2ǜ/"
  - "xform/('|^)ǹg(?=^|$|')/'<b>èng/"
  - "xform/('|^)ňg(?=^|$|')/'<b>ěng/"
  - "xform/('|^)ńg(?=^|$|')/'<b>éng/"
  - "xform/('|^)ng(?=^|$|')/'<b>eng/"
  - "xform/('|^)ǹ(?=^|$|')/'<b>èn/"
  - "xform/('|^)ň(?=^|$|')/'<b>ěn/"
  - "xform/('|^)ń(?=^|$|')/'<b>én/"
  - "xform/('|^)n(?=^|$|')/'<b>en/"
  - "xform/('|^)sh(?=([iuü]?[āáǎàōóǒòēéěèīíǐìūúǔùǖǘǚǜü]))(?!$|')/'<t>/"
  - "xform/('|^)ch(?=([iuü]?[āáǎàōóǒòēéěèīíǐìūúǔùǖǘǚǜü]))(?!$|')/'<s>/"
  - "xform/('|^)zh(?=([iuü]?[āáǎàōóǒòēéěèīíǐìūúǔùǖǘǚǜü]))(?!$|')/'<k>/"
  - "xform/('|^)a(?=([iuü]?[āáǎàōóǒòēéěèīíǐìūúǔùǖǘǚǜü]))(?!$|')/'<q>/"
  - "xform/('|^)p(?=([iuü]?[āáǎàōóǒòēéěèīíǐìūúǔùǖǘǚǜü]))(?!$|')/'<w>/"
  - "xform/('|^)j(?=([iuü]?[āáǎàōóǒòēéěèīíǐìūúǔùǖǘǚǜü]))(?!$|')/'<e>/"
  - "xform/('|^)o(?=([iuü]?[āáǎàōóǒòēéěèīíǐìūúǔùǖǘǚǜü]))(?!$|')/'<r>/"
  - "xform/('|^)n(?=([iuü]?[āáǎàōóǒòēéěèīíǐìūúǔùǖǘǚǜü]))(?!$|')/'<y>/"
  - "xform/('|^)k(?=([iuü]?[āáǎàōóǒòēéěèīíǐìūúǔùǖǘǚǜü]))(?!$|')/'<u>/"
  - "xform/('|^)b(?=([iuü]?[āáǎàōóǒòēéěèīíǐìūúǔùǖǘǚǜü]))(?!$|')/'<i>/"
  - "xform/('|^)c(?=([iuü]?[āáǎàōóǒòēéěèīíǐìūúǔùǖǘǚǜü]))(?!$|')/'<x>/"
  - "xform/('|^)t(?=([iuü]?[āáǎàōóǒòēéěèīíǐìūúǔùǖǘǚǜü]))(?!$|')/'<o>/"
  - "xform/('|^)m(?=([iuü]?[āáǎàōóǒòēéěèīíǐìūúǔùǖǘǚǜü]))(?!$|')/'<p>/"
  - "xform/('|^)q(?=([iuü]?[āáǎàōóǒòēéěèīíǐìūúǔùǖǘǚǜü]))(?!$|')/'<a>/"
  - "xform/('|^)l(?=([iuü]?[āáǎàōóǒòēéěèīíǐìūúǔùǖǘǚǜü]))(?!$|')/'<d>/"
  - "xform/('|^)r(?=([iuü]?[āáǎàōóǒòēéěèīíǐìūúǔùǖǘǚǜü]))(?!$|')/'<f>/"
  - "xform/('|^)w(?=([iuü]?[āáǎàōóǒòēéěèīíǐìūúǔùǖǘǚǜü]))(?!$|')/'<g>/"
  - "xform/('|^)d(?=([iuü]?[āáǎàōóǒòēéěèīíǐìūúǔùǖǘǚǜü]))(?!$|')/'<h>/"
  - "xform/('|^)y(?=([iuü]?[āáǎàōóǒòēéěèīíǐìūúǔùǖǘǚǜü]))(?!$|')/'<j>/"
  - "xform/('|^)g(?=([iuü]?[āáǎàōóǒòēéěèīíǐìūúǔùǖǘǚǜü]))(?!$|')/'<l>/"
  - "xform/('|^)s(?=([iuü]?[āáǎàōóǒòēéěèīíǐìūúǔùǖǘǚǜü]))(?!$|')/'<z>/"
  - "xform/('|^)x(?=([iuü]?[āáǎàōóǒòēéěèīíǐìūúǔùǖǘǚǜü]))(?!$|')/'<c>/"
  - "xform/('|^)f(?=([iuü]?[āáǎàōóǒòēéěèīíǐìūúǔùǖǘǚǜü]))(?!$|')/'<v>/"
  - "xform/('|^)e(?=([iuü]?[āáǎàōóǒòēéěèīíǐìūúǔùǖǘǚǜü]))(?!$|')/'<b>/"
  - "xform/('|^)z(?=([iuü]?[āáǎàōóǒòēéěèīíǐìūúǔùǖǘǚǜü]))(?!$|')/'<n>/"
  - "xform/('|^)h(?=([iuü]?[āáǎàōóǒòēéěèīíǐìūúǔùǖǘǚǜü]))(?!$|')/'<m>/"
  - "xform/('|^)<([a-z])>(āi|ai|óng|ìng|uāng|uang|á|ēr|er|iǎ)(?=^|$|')/$1$2<a>/"
  - "xform/('|^)<([a-z])>(ài|ián|üě|uě|uí|uǎn|uàng|ǘ)(?=^|$|')/$1$2<b>/"
  - "xform/('|^)<([a-z])>(ǔ|uàn)(?=^|$|')/$1$2<c>/"
  - "xform/('|^)<([a-z])>(ǒu|èi|é|iāo|iao|iá)(?=^|$|')/$1$2<d>/"
  - "xform/('|^)<([a-z])>(ü|ǖ|iù|ā|a|uì|uǎng)(?=^|$|')/$1$2<e>/"
  - "xform/('|^)<([a-z])>(ǐ|uān|uan|uà|ò|uǎ)(?=^|$|')/$1$2<f>/"
  - "xform/('|^)<([a-z])>(ī|i|ěn|ěi|uáng)(?=^|$|')/$1$2<g>/"
  - "xform/('|^)<([a-z])>(ǜ|ēng|eng|iē|ie|éi|ún|uāi|uai)(?=^|$|')/$1$2<h>/"
  - "xform/('|^)<([a-z])>(ù|uán|ué|üé)(?=^|$|')/$1$2<i>/"
  - "xform/('|^)<([a-z])>(ái|ǒ|ū|u|ér|īng|ing|uǎi)(?=^|$|')/$1$2<j>/"
  - "xform/('|^)<([a-z])>(ē|e|íng|òu|ié)(?=^|$|')/$1$2<k>/"
  - "xform/('|^)<([a-z])>(ì|uī|ui)(?=^|$|')/$1$2<l>/"
  - "xform/('|^)<([a-z])>(ú|iāng|iang|ěng|ǔn)(?=^|$|')/$1$2<m>/"
  - "xform/('|^)<([a-z])>(í|iǎng|ǎn)(?=^|$|')/$1$2<n>/"
  - "xform/('|^)<([a-z])>(éng|iā|ia|ān|an|èr|uè|üè|iu|iū|uài|iáo)(?=^|$|')/$1$2<o>/"
  - "xform/('|^)<([a-z])>(uǒ|iè|iào|ěr|ǎng)(?=^|$|')/$1$2<p>/"
  - "xform/('|^)<([a-z])>(ēn|en|iǎn|ǒng|iáng|uá|ó|án|ǐng)(?=^|$|')/$1$2<q>/"
  - "xform/('|^)<([a-z])>(ìn|è|à|ě|ǎi|áo)(?=^|$|')/$1$2<r>/"
  - "xform/('|^)<([a-z])>(uō|uo|ǚ|uó|àn|ín|ūn|un|iōng|iong)(?=^|$|')/$1$2<s>/"
  - "xform/('|^)<([a-z])>(ō|o|iòng|ǎo|óu)(?=^|$|')/$1$2<t>/"
  - "xform/('|^)<([a-z])>(àng|iàn|ao|āo)(?=^|$|')/$1$2<u>/"
  - "xform/('|^)<([a-z])>(ǎ|uái|ōng|ong|īn|in|ióng|iǒng)(?=^|$|')/$1$2<v>/"
  - "xform/('|^)<([a-z])>(ào|èn|èng|iě|iàng)(?=^|$|')/$1$2<w>/"
  - "xform/('|^)<([a-z])>(uò|ōu|ou|iān|ian|ēi|ei|ùn)(?=^|$|')/$1$2<x>/"
  - "xform/('|^)<([a-z])>(én|ià|ang|āng|iú|ǐn|üē|uē|üe|ue)(?=^|$|')/$1$2<y>/"
  - "xform/('|^)<([a-z])>(uā|ua|òng|uǐ|áng|iǔ|iǎo)(?=^|$|')/$1$2<z>/"
  - "xform/'|<|>//"
  - "derive/le/dk/"
  - "derive/ma/pe/"
  - "derive/^.+$/\\L$0/"
  - "erase/.*[^a-zA-Z].+$/"
"紫光双拼":
  - "derive/(?<!\\d)1([1-9])(?!\\d)/'shí'$1/"
  - "derive/([1-9])0000(?!0)/$1'wàn'/"
  - "derive/([1-9])000(?!0)/$1'qiān'/"
  - "derive/([1-9])00(?!0)/$1'bǎi'/"
  - "derive/([2-9])0(?!0)/$1'shí'/"
  - "derive/(?<!\\d)([2-9])([1-9])(?!\\d)/$1'shí'$2/"
  - "derive/1([4-7|9])/$1'teen'/"
  - "derive/11/'eleven'/"
  - "derive/12/'twelve'/"
  - "derive/13/'thirteen'/"
  - "derive/15/'fifteen'/"
  - "derive/18/'eighteen'/"
  - "derive/0/o/"
  - "derive/0/O/"
  - "derive/0/'zero'/"
  - "derive/1/'one'/"
  - "derive/10/'ten'/"
  - "derive/2/'to'/"
  - "derive/2/'two'/"
  - "derive/3/'three'/"
  - "derive/4/'for'/"
  - "derive/4/'four'/"
  - "derive/5/'five'/"
  - "derive/6/'six'/"
  - "derive/7/'seven'/"
  - "derive/8/'eight'/"
  - "derive/9/'nine'/"
  - "derive/10/'shí'/"
  - "derive/0/'líng'/"
  - "derive/1/'yī'/"
  - "derive/2/'èr'/"
  - "derive/3/'sān'/"
  - "derive/4/'sì'/"
  - "derive/5/'wǔ'/"
  - "derive/6/'liù'/"
  - "derive/7/'qī'/"
  - "derive/8/'bā'/"
  - "derive/9/'jiǔ'/"
  - "derive/\\+/'plus'/"
  - "derive/\\./'dot'/"
  - "derive/@/'at'/"
  - "derive/-/'hyphen'/"
  - "derive/#/'hash'/"
  - "derive/#/'number'/"
  - "derive/#/'sharp'/"
  - "derive/♯/'sharp'/"
  - "derive / 'slash'"
  - "derive/&/'and'/"
  - "derive/%/'percent'/"
  - "derive/--/'jiǎn'jiǎn'/"
  - "derive/^(.*)-$/$1'jiǎn/"
  - "derive/-/'/"
  - "derive/_/'/"
  - "derive/\\./'diǎn'/"
  - "derive/\\+/'jiā'/"
  - "derive/·/'/"
  - "derive/#/'jǐng'/"
  - "derive/[@]/'ài'tè'/"
  - "derive/[@]/'quān'a'/"
  - "xform/'+/'/"
  - "xform/^'(.*)$/$1/"
  - "xform/^(.*)'$/$1/"
  - "xlit/āáǎàōóǒòēéěèīíǐìūúǔùǖǘǚǜü/aaaaooooeeeeiiiiuuuuvvvvv/"
  - "derive/('|^)([jqxy])u(?=^|$|')/$1$2v/"
  - "xform/('|^)([aoe].*)(?=^|$|')/$1<o>$2/"
  - "xform/('|^)((?:<v>|<i>|<u>|[wrtpsdfghkzcbnm]))en(?=^|$|')/$1$2<w>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[wrtpsdfghklzcbnm]))eng(?=^|$|')/$1$2<t>/"
  - "xform/('|^)([qypjlmnbx])in(?=^|$|')|('|^)((?:<i>|<u>|[gh]))uai(?=^|$|')/$1$2<y>/"
  - "xform/^zh/<u>/"
  - "xform/^sh/<i>/"
  - "xform/'zh/'<u>/"
  - "xform/'sh/'<i>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[tsdghklzcn]))uo(?=^|$|')/$1$2<o>/"
  - "xform/('|^)((?:<i>|<u>|[aoewtpsdghklzcbnm]))ai(?=^|$|')/$1$2<p>/"
  - "xform/^ch/<a>/"
  - "xform/'ch/'<a>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[aoeqghjkxbn]))[iu]ang(?=^|$|')/$1$2<g>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[wrtypsdfghklzcbnm]))ang(?=^|$|')/$1$2<s>/"
  - "xform/('|^)([qtpdjlxbnm])ie(?=^|$|')/$1$2<d>/"
  - "xform/('|^)([qtpdjlxbnm])ian(?=^|$|')/$1$2<f>/"
  - "xform/('|^)((?:<v>|<i>|[qrtysdghjklzxcn]))i?ong(?=^|$|')/$1$2<h>/"
  - "xform/('|^)er(?=^|$|')/$1<j>/"
  - "xform/('|^)([qdjlxnm])iu(?=^|$|')/$1$2<j>/"
  - "xform/('|^)((?:<v>|[aoewtpdfghklzvbnm]))ei(?=^|$|')/$1$2<k>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[qrtysdghjklzxcn]))uan(?=^|$|')/$1$2<l>/"
  - "xform/('|^)([qtypdjlxnm])ing(?=^|$|')/$1$2;/"
  - "xform/('|^)((?:<v>|<i>|<u>|[rtypsdfghklzm]))ou(?=^|$|')/$1$2<z>/"
  - "xform/('|^)([tghkljx])[iu]a(?=^|$|')/$1$2<x>/"
  - "xform/('|^)([qtpdjlxbnm])iao(?=^|$|')/$1$2<b>/"
  - "xform/('|^)([qyjx])ue(?=^|$|')|('|^)((?:<v>|<i>|<u>|[tsdghkzc]))ui(?=^|$|')|('|^)([ln])ve(?=^|$|')/$1$2<n>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[qrtysdghjklzxc]))un(?=^|$|')/$1$2<m>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[rtypsdghklzcbnm]))ao(?=^|$|')/$1$2<q>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[wrtypsdfghklzcbnm]))an(?=^|$|')/$1$2<r>/"
  - "xform/'|<|>//"
  - "derive/^.+$/\\L$0/"
  - "erase/.*[^a-zA-Z].+$/"
"自然码":
  - "derive/(?<!\\d)1([1-9])(?!\\d)/'shí'$1/"
  - "derive/([1-9])0000(?!0)/$1'wàn'/"
  - "derive/([1-9])000(?!0)/$1'qiān'/"
  - "derive/([1-9])00(?!0)/$1'bǎi'/"
  - "derive/([2-9])0(?!0)/$1'shí'/"
  - "derive/(?<!\\d)([2-9])([1-9])(?!\\d)/$1'shí'$2/"
  - "derive/1([4-7|9])/$1'teen'/"
  - "derive/11/'eleven'/"
  - "derive/12/'twelve'/"
  - "derive/13/'thirteen'/"
  - "derive/15/'fifteen'/"
  - "derive/18/'eighteen'/"
  - "derive/0/o/"
  - "derive/0/O/"
  - "derive/0/'zero'/"
  - "derive/1/'one'/"
  - "derive/10/'ten'/"
  - "derive/2/'to'/"
  - "derive/2/'two'/"
  - "derive/3/'three'/"
  - "derive/4/'for'/"
  - "derive/4/'four'/"
  - "derive/5/'five'/"
  - "derive/6/'six'/"
  - "derive/7/'seven'/"
  - "derive/8/'eight'/"
  - "derive/9/'nine'/"
  - "derive/10/'shí'/"
  - "derive/0/'líng'/"
  - "derive/1/'yī'/"
  - "derive/2/'èr'/"
  - "derive/3/'sān'/"
  - "derive/4/'sì'/"
  - "derive/5/'wǔ'/"
  - "derive/6/'liù'/"
  - "derive/7/'qī'/"
  - "derive/8/'bā'/"
  - "derive/9/'jiǔ'/"
  - "derive/\\+/'plus'/"
  - "derive/\\./'dot'/"
  - "derive/@/'at'/"
  - "derive/-/'hyphen'/"
  - "derive/#/'hash'/"
  - "derive/#/'number'/"
  - "derive/#/'sharp'/"
  - "derive/♯/'sharp'/"
  - "derive / 'slash'"
  - "derive/&/'and'/"
  - "derive/%/'percent'/"
  - "derive/--/'jiǎn'jiǎn'/"
  - "derive/^(.*)-$/$1'jiǎn/"
  - "derive/-/'/"
  - "derive/_/'/"
  - "derive/\\./'diǎn'/"
  - "derive/\\+/'jiā'/"
  - "derive/·/'/"
  - "derive/#/'jǐng'/"
  - "derive/[@]/'ài'tè'/"
  - "derive/[@]/'quān'a'/"
  - "xform/'+/'/"
  - "xform/^'(.*)$/$1/"
  - "xform/^(.*)'$/$1/"
  - "xlit/āáǎàōóǒòēéěèīíǐìūúǔùǖǘǚǜü/aaaaooooeeeeiiiiuuuuvvvvv/"
  - "derive/('|^)([jqxy])u(?=^|$|')/$1$2v/"
  - "derive/('|^)([aoe])([ioun])(?=^|$|')/$1$2$2$3/"
  - "xform/('|^)([aoe])(ng)?(?=^|$|')/$1$2$2$3/"
  - "xform/('|^)([qdjlxnm])iu(?=^|$|')/$1$2<q>/"
  - "xform/('|^)([tghkljx])[iu]a(?=^|$|')/$1$2<w>/"
  - "xform/('|^)([qyjlxn])[uv]e(?=^|$|')/$1$2<t>/"
  - "xform/^sh/<u>/"
  - "xform/^ch/<i>/"
  - "xform/^zh/<v>/"
  - "xform/'sh/'<u>/"
  - "xform/'ch/'<i>/"
  - "xform/'zh/'<v>/"
  - "xform/('|^)([qtypdjlxnm])ing(?=^|$|')|('|^)((?:<i>|<u>|[gh]))uai(?=^|$|')/$1$2<y>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[qrtysdghjklzxcn]))[uv]an(?=^|$|')/$1$2<r>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[tsdghklzcn]))uo(?=^|$|')/$1$2<o>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[qrtysdghjklzxc]))[uv]n(?=^|$|')/$1$2<p>/"
  - "xform/('|^)((?:<v>|<i>|[qrtysdghjklzxcn]))i?ong(?=^|$|')/$1$2<s>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[aoeqghjkxbn]))[iu]ang(?=^|$|')/$1$2<d>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[wrtpsdfghkzcbnm]))en(?=^|$|')/$1$2<f>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[wrtpsdfghklzcbnm]))eng(?=^|$|')/$1$2<g>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[wrtypsdfghklzcbnm]))ang(?=^|$|')/$1$2<h>/"
  - "xform/('|^)([qtpdjlxbnm])ian(?=^|$|')/$1$2<m>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[wrtypsdfghklzcbnm]))an(?=^|$|')/$1$2<j>/"
  - "xform/('|^)([qtpdjlxbnm])iao(?=^|$|')/$1$2<c>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[rtypsdghklzcbnm]))ao(?=^|$|')/$1$2<k>/"
  - "xform/('|^)((?:<i>|<u>|[aoewtpsdghklzcbnm]))ai(?=^|$|')/$1$2<l>/"
  - "xform/('|^)((?:<v>|[aoewtpdfghklzvbnm]))ei(?=^|$|')/$1$2<z>/"
  - "xform/('|^)([qtpdjlxbnm])ie(?=^|$|')/$1$2<x>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[tsdghkzc]))ui(?=^|$|')/$1$2<v>/"
  - "xform/('|^)((?:<v>|<i>|<u>|[rtypsdfghklzm]))ou(?=^|$|')/$1$2<b>/"
  - "xform/('|^)([qypjlmnbx])in(?=^|$|')/$1$2<n>/"
  - "xform/'|<|>//"
  - "derive/^.+$/\\L$0/"
  - "erase/.*[^a-zA-Z].+$/"
"自然龙":
  - "derive/(?<!\\d)1([1-9])(?!\\d)/'shí'$1/"
  - "derive/([1-9])0000(?!0)/$1'wàn'/"
  - "derive/([1-9])000(?!0)/$1'qiān'/"
  - "derive/([1-9])00(?!0)/$1'bǎi'/"
  - "derive/([2-9])0(?!0)/$1'shí'/"
  - "derive/(?<!\\d)([2-9])([1-9])(?!\\d)/$1'shí'$2/"
  - "derive/1([4-7|9])/$1'teen'/"
  - "derive/11/'eleven'/"
  - "derive/12/'twelve'/"
  - "derive/13/'thirteen'/"
  - "derive/15/'fifteen'/"
  - "derive/18/'eighteen'/"
  - "derive/0/o/"
  - "derive/0/O/"
  - "derive/0/'zero'/"
  - "derive/1/'one'/"
  - "derive/10/'ten'/"
  - "derive/2/'to'/"
  - "derive/2/'two'/"
  - "derive/3/'three'/"
  - "derive/4/'for'/"
  - "derive/4/'four'/"
  - "derive/5/'five'/"
  - "derive/6/'six'/"
  - "derive/7/'seven'/"
  - "derive/8/'eight'/"
  - "derive/9/'nine'/"
  - "derive/10/'shí'/"
  - "derive/0/'líng'/"
  - "derive/1/'yī'/"
  - "derive/2/'èr'/"
  - "derive/3/'sān'/"
  - "derive/4/'sì'/"
  - "derive/5/'wǔ'/"
  - "derive/6/'liù'/"
  - "derive/7/'qī'/"
  - "derive/8/'bā'/"
  - "derive/9/'jiǔ'/"
  - "derive/\\+/'plus'/"
  - "derive/\\./'dot'/"
  - "derive/@/'at'/"
  - "derive/-/'hyphen'/"
  - "derive/#/'hash'/"
  - "derive/#/'number'/"
  - "derive/#/'sharp'/"
  - "derive/♯/'sharp'/"
  - "derive / 'slash'"
  - "derive/&/'and'/"
  - "derive/%/'percent'/"
  - "derive/--/'jiǎn'jiǎn'/"
  - "derive/^(.*)-$/$1'jiǎn/"
  - "derive/-/'/"
  - "derive/_/'/"
  - "derive/\\./'diǎn'/"
  - "derive/\\+/'jiā'/"
  - "derive/·/'/"
  - "derive/#/'jǐng'/"
  - "derive/[@]/'ài'tè'/"
  - "derive/[@]/'quān'a'/"
  - "xform/'+/'/"
  - "xform/^'(.*)$/$1/"
  - "xform/^(.*)'$/$1/"
  - "xform/('|^)(ā|á|ǎ|à)([ioun])(?=^|$|')/'a$2$3/"
  - "xform/('|^)(ō|ó|ǒ|ò)([ioun])(?=^|$|')/'o$2$3/"
  - "xform/('|^)(ē|é|ě|è)([ioun])(?=^|$|')/'e$2$3/"
  - "xform/('|^)(ā|á|ǎ|à)(ng)(?=^|$|')/'a$2$3/"
  - "xform/('|^)(ō|ó|ǒ|ò)(ng)(?=^|$|')/'o$2$3/"
  - "xform/('|^)(ē|é|ě|è)(ng)(?=^|$|')/'e$2$3/"
  - "xform/('|^)(ā|á|ǎ|à)(?=^|$|')/'a$2/"
  - "xform/('|^)(ō|ó|ǒ|ò)(?=^|$|')/'o$2/"
  - "xform/('|^)(ē|é|ě|è)(?=^|$|')/'e$2/"
  - "xform/(')([jqxy])u(?=^|$|')/'$2ü/"
  - "xform/('|^)([jqxy])ū(?=^|$|')/'$2ǖ/"
  - "xform/('|^)([jqxy])ú(?=^|$|')/'$2ǘ/"
  - "xform/('|^)([jqxy])ǔ(?=^|$|')/'$2ǚ/"
  - "xform/('|^)([jqxy])ù(?=^|$|')/'$2ǜ/"
  - "xform/('|^)ǹg(?=^|$|')/'eèng/"
  - "xform/('|^)ňg(?=^|$|')/'eěng/"
  - "xform/('|^)ńg(?=^|$|')/'eéng/"
  - "xform/('|^)ng(?=^|$|')/'eeng/"
  - "xform/('|^)ǹ(?=^|$|')/'eèn/"
  - "xform/('|^)ň(?=^|$|')/'eěn/"
  - "xform/('|^)ń(?=^|$|')/'eén/"
  - "xform/('|^)n(?=^|$|')/'een/"
  - "xform/('|^)ēr(?=^|$|')/'e<q>/"
  - "xform/('|^)ér(?=^|$|')/'e<k>/"
  - "xform/('|^)ěr(?=^|$|')/'e<u>/"
  - "xform/('|^)èr(?=^|$|')/'e<h>/"
  - "xform/('|^)er(?=^|$|')/'e<q>/"
  - "xform/('|^)a(?=^|$|')/'aā/"
  - "xform/('|^)o(?=^|$|')/'oō/"
  - "xform/('|^)e(?=^|$|')/'eē/"
  - "xform/'sh/'u/"
  - "xform/'ch/'i/"
  - "xform/'zh/'v/"
  - "xform/^sh(?=([iuü]?[āáǎàōóǒòēéěèīíǐìūúǔùǖǘǚǜü]))(?!$|')/u/"
  - "xform/^ch(?=([iuü]?[āáǎàōóǒòēéěèīíǐìūúǔùǖǘǚǜü]))(?!$|')/i/"
  - "xform/^zh(?=([iuü]?[āáǎàōóǒòēéěèīíǐìūúǔùǖǘǚǜü]))(?!$|')/v/"
  - "xform/('|^)([a-z])(iáo|iǎng|uǎng|āng|uē|üe|ǖe|ǎi|á)(?=^|$|')/$1$2<u>/"
  - "xform/('|^)([a-z])(iàng|iǒng|uàng|ēn|īng|é|ó)(?=^|$|')/$1$2<e>/"
  - "xform/('|^)([a-z])(iǎn|iōng|uǎi|uò|ǎng|ō)(?=^|$|')/$1$2<p>/"
  - "xform/('|^)([a-z])(uāng|ǐng|ìng|uí|áng)(?=^|$|')/$1$2<w>/"
  - "xform/('|^)([a-z])(uǎn|uái||uā|én|uō|ié|ǚ)(?=^|$|')/$1$2<s>/"
  - "xform/('|^)([a-z])(uán|ài|ěn|èn|uě|ǚe|ǎn|ǔn|iù)(?=^|$|')/$1$2<o>/"
  - "xform/('|^)([a-z])(uān|àng|ái|iā|uè|üè)(?=^|$|')/$1$2<d>/"
  - "xform/('|^)([a-z])(iáng|áo|ué|üé|ēi|à|è|ǒ)(?=^|$|')/$1$2<i>/"
  - "xform/('|^)([a-z])(uāi|uà|uǎ|ūn|ò|ǐ)(?=^|$|')/$1$2<g>/"
  - "xform/('|^)([a-z])(éng|èng|uài|èi|uì|ǜ|ún)(?=^|$|')/$1$2<f>/"
  - "xform/('|^)([a-z])(ióng|ōng|án|iē|ie)(?=^|$|')/$1$2<k>/"
  - "xform/('|^)([a-z])(iào|iǎo|uǒ|uó|a|ā|ě|ú)(?=^|$|')/$1$2<l>/"
  - "xform/('|^)([a-z])(uàn|ēng|eng|iá|ín|iě)(?=^|$|')/$1$2<c>/"
  - "xform/('|^)([a-z])(iān|òu|éi|ùn|ē)(?=^|$|')/$1$2<r>/"
  - "xform/('|^)([a-z])(iāng|ěng|òng)(?=^|$|')/$1$2<y>/"
  - "xform/('|^)([a-z])(iāo|ǔ|ǎ|iú|ǘ)(?=^|$|')/$1$2<m>/"
  - "xform/('|^)([a-z])(iǎ|íng|ān|ǒng)(?=^|$|')/$1$2<n>/"
  - "xform/('|^)([a-z])(iòng|īn|ǖ|ù)(?=^|$|')/$1$2<h>/"
  - "xform/('|^)([a-z])(ǎo|ià|ǐn|ōu|ou)(?=^|$|')/$1$2<x>/"
  - "xform/('|^)([a-z])(óng|àn|ěi|ī|i)(?=^|$|')/$1$2<j>/"
  - "xform/('|^)([a-z])(ián|ào|ìn|uǐ)(?=^|$|')/$1$2<v>/"
  - "xform/('|^)([a-z])(uáng|āi|í)(?=^|$|')/$1$2<b>/"
  - "xform/('|^)([a-z])(ǒu|iū|iǔ|iu)(?=^|$|')/$1$2<z>/"
  - "xform/('|^)([a-z])(uá|uī|ì)(?=^|$|')/$1$2<t>/"
  - "xform/('|^)([a-z])(ū|óu|iàn)(?=^|$|')/$1$2<a>/"
  - "xform/('|^)([a-z])(āo|iè)(?=^|$|')/$1$2<q>/"
  - "xform/(')([a-z])ei(?=^|$|')/$1$2<i>/"
  - "xform/(')([a-z])un(?=^|$|')/$1$2<g>/"
  - "xform/(')([a-z])an(?=^|$|')/$1$2<n>/"
  - "xform/(')([a-z])ai(?=^|$|')/$1$2<b>/"
  - "xform/(')([a-z])in(?=^|$|')/$1$2<h>/"
  - "xform/(')([a-z])iao(?=^|$|')/$1$2<m>/"
  - "xform/(')([a-z])iang(?=^|$|')/$1$2<y>/"
  - "xform/(')([a-z])ui(?=^|$|')/$1$2<t>/"
  - "xform/(')([a-z])u(?=^|$|')/$1$2<a>/"
  - "xform/(')([a-z])ao(?=^|$|')/$1$2<q>/"
  - "xform/(')([a-z])e(?=^|$|')/$1$2<r>/"
  - "xform/'|<|>//"
  - "derive/al/a/"
  - "derive/op/o/"
  - "derive/^.+$/\\L$0/"
  - "erase/.*[^a-zA-Z].+$/"
"通用派生规则":
  - "derive/(?<!\\d)1([1-9])(?!\\d)/'shí'$1/"
  - "derive/([1-9])0000(?!0)/$1'wàn'/"
  - "derive/([1-9])000(?!0)/$1'qiān'/"
  - "derive/([1-9])00(?!0)/$1'bǎi'/"
  - "derive/([2-9])0(?!0)/$1'shí'/"
  - "derive/(?<!\\d)([2-9])([1-9])(?!\\d)/$1'shí'$2/"
  - "derive/1([4-7|9])/$1'teen'/"
  - "derive/11/'eleven'/"
  - "derive/12/'twelve'/"
  - "derive/13/'thirteen'/"
  - "derive/15/'fifteen'/"
  - "derive/18/'eighteen'/"
  - "derive/0/o/"
  - "derive/0/O/"
  - "derive/0/'zero'/"
  - "derive/1/'one'/"
  - "derive/10/'ten'/"
  - "derive/2/'to'/"
  - "derive/2/'two'/"
  - "derive/3/'three'/"
  - "derive/4/'for'/"
  - "derive/4/'four'/"
  - "derive/5/'five'/"
  - "derive/6/'six'/"
  - "derive/7/'seven'/"
  - "derive/8/'eight'/"
  - "derive/9/'nine'/"
  - "derive/10/'shí'/"
  - "derive/0/'líng'/"
  - "derive/1/'yī'/"
  - "derive/2/'èr'/"
  - "derive/3/'sān'/"
  - "derive/4/'sì'/"
  - "derive/5/'wǔ'/"
  - "derive/6/'liù'/"
  - "derive/7/'qī'/"
  - "derive/8/'bā'/"
  - "derive/9/'jiǔ'/"
  - "derive/\\+/'plus'/"
  - "derive/\\./'dot'/"
  - "derive/@/'at'/"
  - "derive/-/'hyphen'/"
  - "derive/#/'hash'/"
  - "derive/#/'number'/"
  - "derive/#/'sharp'/"
  - "derive/♯/'sharp'/"
  - "derive / 'slash'"
  - "derive/&/'and'/"
  - "derive/%/'percent'/"
  - "derive/--/'jiǎn'jiǎn'/"
  - "derive/^(.*)-$/$1'jiǎn/"
  - "derive/-/'/"
  - "derive/_/'/"
  - "derive/\\./'diǎn'/"
  - "derive/\\+/'jiā'/"
  - "derive/·/'/"
  - "derive/#/'jǐng'/"
  - "derive/[@]/'ài'tè'/"
  - "derive/[@]/'quān'a'/"
  - "xform/'+/'/"
  - "xform/^'(.*)$/$1/"
  - "xform/^(.*)'$/$1/"