# Rime schema
# encoding: utf-8
#############DIY你想要的方案组合,试试搭配一个自然码+墨奇辅助的方案吧！###########################
set_shuru_schema:              #配置此项就是选择什么输入法,同时拆分反查和中英文混输也将匹配该输入方案
  __include: 自然码             #可选解码规则有   自然码, 自然龙, 汉心龙, 小鹤双拼, 搜狗双拼, 微软双拼, 智能ABC, 紫光双拼， 国标双拼， 龙三 选择一个填入
set_fuzhu_schema:              #配置此项就是选择什么输入法,同时拆分反查和中英文混输也将匹配该输入方案
  __include: 直接辅助           #可选解码规则有:  直接辅助, 间接辅助  选择一个填入
super_comment:                 # 超级注释模块，子项配置 true 开启，false 关闭
  candidate_length: 1          # 候选词辅助码提醒的生效长度，0为关闭  但同时清空其它，应当使用上面开关来处理
  corrector_type: "{comment}"  # 换一种显示类型，比如"({comment})" 
  fuzhu_type: fuzhu            # 分包这是一个固定搭配，声调可以使用开关控制，这里无需修改
__include: octagram            #启用语法模型
#__include: set_chord_composer  #启用并击处理，不懂得不要开启就是了
########################以下是方案配置######################################################

# 方案说明
schema:
  schema_id: wanxiang_pro
  name: 万象拼音PRO
  version: "LTS"
  author:
    - amzxyz
  description: |
    万象拼音 支持常见双拼方案和全拼输入，辅助码同时支持 全拼拼音；墨奇码;鹤形;自然码;简单鹤;仓颉;虎码首末;五笔前2;汉心码; 采用万象归一的词库，换方案不换词库，不丢失用户词！
  dependencies:
    - wanxiang_mixedcode  #中英文混合词汇
    - wanxiang_reverse    # 部件拆字，反查及辅码
    - wanxiang_chaifen    #辅助码拆分注释、翻译注释、行政区划匹配、车牌、等等注释类显示滤镜，Lua专用：super_comment
    - wanxiang_charset    #字符集过滤器

# 开关
# reset: 默认状态。注释掉后，切换窗口时不会重置到默认状态。
# states: 方案选单显示的名称。可以注释掉，仍可以通过快捷键切换。
# abbrev: 默认的缩写取 states 的第一个字符，abbrev 可自定义一个字符
switches:
  - name: ascii_mode                   # 中英输入状态
    states: [ 中文, 英文 ]
  - name: ascii_punct                  # 中英标点
    states: [ 中标, 英标 ]
  - name: full_shape                   #全角、半角字符输出
    states: [ 半角, 全角 ]
  - name: emoji                        #候选出现emoji滤镜，会显示在相应的候选后面，万象侧重于tips提示，避免候选被占用，因此默认为reset: 0，归属opencc emoji滤镜
    states: [ 表情关, 表情开 ]
  - name: chinese_english              #候选进入翻译模式滤镜，会显示在相应的候选后面，万象侧重于tips提示，避免候选被占用，快捷键配套ctrl+e,归属opencc 翻译滤镜
    states: [ 翻译关, 翻译开 ]
  - name: chaifen_switch               #开启后在候选的注释里面实时显示辅助码的拆分提醒优先级高于普通辅助提醒，不开启则采用系统配置，快捷键配套ctrl+c,影响的是comment_format，归属：super_comment.lua
    states: [ 拆分关, 拆分开 ]
  - name: charset_filter               #字符集过滤，默认开启8105通规显示，即小字集，可通过开关实时开启全字集，快捷键配套ctrl+g,归属：chars_filter.lua
    states: [ 小字集, 大字集 ]
  - name: super_tips                   #开启后在输入编码后面的提示区显示实时的提示数据，受tips数据库影响，表情、翻译、车牌、符号等对应关系数据，并可实现句号上屏，不开启则默认，影响的是segment.prompt参数，归属：super_tips.lua
    states: [ 提示关, 提示开 ]
    reset: 1
  - options: [ raw_input, tone_display, full_pinyin ]   #开启后在输入编码的位置实时转换为带声调全拼或者不带声调全拼，不开启则采用系统配置原始编码，影响的是preedit_format,归属：super_preedit.lua
    states: [ 原编码, 有声调, 无声调 ]
#    reset: 2     #对于开关组从0开始数，第几个就reset几可设为默认
  - options: [ comment_off, fuzhu_hint, tone_hint ]  #开启后在候选的注释里面实时显示辅助码或者全拼声调，不开启则采用系统配置，影响的是comment_format，快捷键配套ctrl+a,归属：super_comment.lua
    states: [ 注释关, 辅助开, 读音开 ]
  - options: [ s2s, s2t, s2hk, s2tw ]  # 简繁转换开关组，可以在一个空选项和多个实际“- simplifier@s2hk”引入的项目之前切换，这是一个开关组，你可以将其中任意一个s2s等设置为toggle快捷键，多次按下将轮询
    states: [ 简体, 通繁, 港繁, 臺繁 ]
  - name: search_single_char          #多体现在编码重合但候选有单字或者多字的情况`引导的辅码查词时是否单字优先，全拼常见，类似于特定编码情况下、反查状态下的调序能力。归属：search.lua
    states: [正常, 单字]


# 输入引擎
engine:
  processors:
    - chord_composer                       #并击处理器，与置顶参数：是否启用并击，选项配合使用
    #- lua_processor@*select_character     #以词定字，默认左中括号上屏一个词的前一个字，右中括号上屏一个词的后一个字
    - lua_processor@*tone_fallback         #声调辅助回退，当你输入声调数字错误时，继续输入正确的而不用回退删除
    - lua_processor@*super_sequence*P      #手动排序，高亮候选 ctrl+j左移动 ctrl+k 右移动 ctrl+l 移除位移  ctrl+p 置顶
    - lua_processor@*quick_symbol_text     #快符引导以及重复上屏，配合quick_symbol_text顶层配置清单定义扩展按键
    - lua_processor@*super_tips            #超级提示模块：表情、简码、翻译、化学式、等等靠你想象
    - lua_processor@*limit_repeated        #用于限制最大候选长度以及最大重复输入声母编码长度，避免性能异常
    - lua_processor@*backspace_limit       #防止连续 Backspace 在编码为空时删除已上屏内容
    - lua_processor@*userdb_sync_delete    #危险操作：通过输入 /del 触发,用于清理自定义同步目录下txt用户词典里被标记c<0的词条，操作步骤：必须先同步、再执行/del、再去部署刷新状态、再去同步回收词典，完成动作。
    - ascii_composer                       #处理英文模式及中英文切换
    - recognizer                           #与 matcher 搭配，处理符合特定规则的输入码，如网址、反查等 tags
    - key_binder                           #在特定条件下将按键绑定到其他按键，如重定义逗号、句号为候选翻页、开关快捷键等
    - lua_processor@*key_binder            #绑定按键扩展能力，支持正则扩展将按键生效情景更加细化
    - speller                              #拼写处理器，接受字符按键，编辑输入
    - punctuator                           #符号处理器，将单个字符按键直接映射为标点符号或文字
    - selector                             #选字处理器，处理数字选字键〔可以换成别的哦〕、上、下候选定位、换页
    - navigator                            #处理输入栏内的光标移动
    - express_editor                       #编辑器，处理空格、回车上屏、回退键
  segmentors:
    - ascii_segmentor                      #标识英文段落〔譬如在英文模式下〕字母直接上屛
    - matcher                              #配合 recognizer 标识符合特定规则的段落，如网址、反查等，加上特定 tag
    - abc_segmentor                        #标识常规的文字段落，加上 abc 这个 tag
    - affix_segmentor@wanxiang_reverse     #反查 tag
    - affix_segmentor@add_user_dict        #自造词加词 tag
    - punct_segmentor                      #标识符号段落〔键入标点符号用〕加上 punct 这个 tag
    - fallback_segmentor                   #标识其他未标识段落，必须放在最后帮助tag模式切换后回退重新处理
  translators:
    - punct_translator                     #配合 punct_segmentor 转换标点符号
    - script_translator                    #脚本翻译器，用于拼音、粤拼等基于音节表的输入方案
    - lua_translator@*version_display      #输入'/wx'，显示万象项目网址和当前版本号
    - lua_translator@*set_schema           #输入'/zrm'，快速切换为自然码双拼， /flypy→小鹤双拼 /mspy→微软双拼 /zrm→自然码 /sogou→搜狗双拼 /abc→智能ABC /ziguang→紫光双拼 /pyjj→拼音加加 /gbpy→国标双拼 /lxsq→乱序17 /pinyin→全拼
    - lua_translator@*shijian              #农历、日期、节气、节日、时间、周、问候模板等等，触发清单看下文
    - lua_translator@*unicode              #通过输入大写U引导，并输入Unicode编码获得汉字输出
    - lua_translator@*number_translator    #数字、金额大写，通过输入大写R1234获得候选输出
    - lua_translator@*super_calculator     #超级计算器，Lua内查看高级用法
    - lua_translator@*input_statistics     #一个输入统计的脚本，以日、周、月、年等维度的统计
    - table_translator@custom_phrase       #自定义短语 custom_phrase.txt，用于置顶自定义编码候选词
    - table_translator@chengyu             #简码成语词汇表导入
    - table_translator@wanxiang_mixedcode  #中英等混合词汇表导入
    - table_translator@wanxiang_reverse    #挂接部件组字和笔画反查
    - script_translator@user_dict_set      #自造词之使用词汇入口
    - script_translator@add_user_dict      #自造词之制造词汇入口
    - lua_translator@*force_gc             #暴力 GC 降低内存
  filters:
    - lua_filter@*chars_filter                      #字符集过滤
    - lua_filter@*super_sequence*F                  #手动排序放在assist_sort 后面，高亮候选 ctrl+j左移动 ctrl+k 右移动 ctrl+0 移除位移
    - lua_filter@*autocap_filter                    #英文自动大写，当输入一个大写那么候选首字母大写，当输入两个大写那么候选全为大写
    - reverse_lookup_filter@radical_reverse_lookup  #部件拆字滤镜，放在super_comment前面，进一步被超级注释处理以获得拼音编码的提示
    - lua_filter@*super_preedit                     #输入码显示全拼并带上音调
    - lua_filter@*super_comment                     #超级注释模块，支持错词提示、辅助码显示，部件组字读音注释，支持个性化配置和关闭相应的功能，详情搜索super_comment进行详细配置
    - simplifier@emoji                              #Emoji滤镜
    - simplifier@s2t                                #简繁切换通繁
    - simplifier@s2tw                               #简繁切换台繁
    - simplifier@s2hk                               #简繁切换港繁
    - simplifier@chinese_english                    #中英翻译滤镜
    - lua_filter@*search@wanxiang_reverse           #部件拆字辅码放在简繁转换之后
    - lua_filter@*text_formatting                   #给自定义用户词扩展一个换行:\n, 制表符：\t, 回车符：\r, 空格：\s
    - uniquifier                                    #去重

#shijian:仅仅作为提示使用，编码已经写死，引导适应性更高，如有需求lua中修改
#时间：osj 或者 /sj
#日期：orq 或者 /rq
#农历：onl 或者 /nl
#星期：oxq 或者 /xq
#今年第几周：oww 或者 /ww
#节气：ojq 或者 /jq
#日期+时间：ors 或者 /rs
#时间戳：ott 或者 /tt
#大写N日期：N20250315
#节日：ojr 或者 /jr
#问候模板：/day 或者 oday
#生日信息：/sr或者osr
birthday_reminder:  #日期格式：必须是4位数字，格式为MMDD（月份和日期），例如：1月27日 → 0127 ，#备注格式：在日期后添加逗号，然后添加任意文本作为备注，例如："0501,我的好朋友"，也可以无备注
  solar_birthdays:  # 公历生日, 姓名: "日期,备注" or 姓名: "日期"
    小明: "0501,准备礼物"
    大明: "0405"
  lunar_birthdays:  # 农历生日, 姓名: "日期,备注" or 姓名: "日期"
    小明: "0114"
    小红: "0815,农历中秋"

#快符lua，lua中有预设，26个字母、10个数字，你可以在这里配置替换默认的值，键可以是一个或多个，值可以是任意字符这里不齐全默认配置将生效
quick_symbol_text:
  q: "欢迎使用万象拼音"
  w: "噢我的天哪！"
  e: "（"
  r: "）"
  t: "~"
  y: "·"
  u: "『"
  i: "』"
  o: "〖"
  p: "〗"
  a: "！"
  s: "……"
  d: "、"
  f: "“"
  g: "”"
  h: "‘"
  j: "’"
  k: "【"
  l: "】"
  z: "。”"
  x: "？”"
  c: "！”"
  v: "——"
  b: "%"
  n: "《"
  m: "》"
  "1": "①"
  "2": "②"
  "3": "③"
  "4": "④"
  "5": "⑤"
  "6": "⑥"
  "7": "⑦"
  "8": "⑧"
  "9": "⑨"
  "0": "⓪"

# Lua 配置：计算器触发关键字
calculator:
  trigger: "V"

# 主翻译器，拼音
translator:
  dictionary: wanxiang_pro          # 挂载词库 wanxiang.dict.yaml
#  packs: user                  #导入根目录下名称为user.dict.yaml的自定义固定词典
#  prism: double_pinyin          # 多方案共用一个词库时，为避免冲突，需要用 prism 指定一个名字。
  enable_completion: true  # 启用候选词补全
#  user_dict: zrm.userdb  # 用户词典的文件名称
#  db_class: tabledb  #开启后就不会产生zrm.userdb这样的文件夹，会直接输出文本zrm.txt同时无法使用同步能力
  enable_user_dict: false # 是否开启自动调频
#  disable_user_dict_for_patterns:
#    - "^[a-z]{1,6}"    #基本的6码3字不调频
  enable_correction: false #是否开启自动纠错
  initial_quality: 3       # 初始质量拼音的权重应该比英文大
  spelling_hints: 50             # 将注释以词典字符串形式完全暴露，通过super_comment.lua完全接管，灵活配置。
  always_show_comments: true    # Rime 默认在 preedit 等于 comment 时取消显示 comment，这里强制一直显示，供super_comment.lua做判断用。
  comment_format: {comment}  #将注释以词典字符串形式完全暴露，通过super_comment.lua完全接管，灵活配置。
  preedit_format:     #影响到输入框的显示和“Shift+回车”上屏的字符，交给super_preedit处理
    - xform/7/¹/
    - xform/8/²/
    - xform/9/³/
    - xform/0/⁴/

# 自定义短语
custom_phrase:
  dictionary: ""
  user_dict: custom_phrase  # 需要手动创建 custom_phrase.txt 文件
  db_class: stabledb
  enable_completion: false # 补全提示
  enable_sentence: false   # 禁止造句
  initial_quality: 99      # custom_phrase 的权重应该比 pinyin 和 wanxiang_en 大


# 简码词库导入位于dicts得txt文件词库
chengyu:
  dictionary: ""
  user_dict: dicts/chengyu
  db_class: stabledb
  enable_sentence: false
  enable_completion: false
  initial_quality: 1.2 #本表词和系统词重码居后

# 中文、英文、数字、符号等混合词汇
wanxiang_mixedcode:
  dictionary: wanxiang_mixedcode
  db_class: stabledb
  enable_completion: true
  enable_sentence: false
  initial_quality: 0.5
  comment_format:
    - xform/^.+$//

# Emoji
emoji:
  option_name: emoji
  opencc_config: emoji.json
  inherit_comment: false

#中文转英文
chinese_english:
  option_name: chinese_english
  opencc_config: chinese_english.json
  tips: char
  comment_format:
    - "xform/-/ /"

# 简繁切换
s2t:
  option_name: s2t
  opencc_config: s2t.json  # s2t.json | s2hk.json | s2tw.json | s2twp.json
  tips: none               # 转换提示: all 都显示 | char 仅单字显示 | none 不显示。
  tags: [ abc ]  # 限制在对应 tag，不对其他如反查的内容做简繁转换

s2hk:
  opencc_config: s2hk.json
  option_name: s2hk
  tags: [abc]

s2tw:
  opencc_config: s2tw.json
  option_name: s2tw
  tags: [abc]

# 部件拆字反查
wanxiang_reverse:
  tag: wanxiang_reverse
  dictionary: wanxiang_reverse
  db_class: stabledb
  enable_user_dict: false
  prefix: "`"  # 反查前缀（反查时前缀会消失影响打英文所以设定为两个字母，或可改成一个非字母符号），与 recognizer/patterns/radical_lookup 匹配
  tips: "〔反查：部件|笔画〕"

# 部件拆字滤镜
radical_reverse_lookup:
  tags: [ wanxiang_reverse ]  #起作用tag范围
  overwrite_comment: true   #是否覆盖其他提示
  dictionary: wanxiang_pro      #带音调的词典


# 处理符合特定规则的输入码，如网址、反查
recognizer:
  import_preset: default  # 从 default.yaml 继承通用的
  patterns:  # 再增加方案专有的：
    punct: "^/([0-9]|10|[A-Za-z]+)$"    # 响应 symbols.yaml 的 symbols
    wanxiang_reverse: "^`[A-Za-z]*$"        # 响应部件拆字与笔画的反查，与 radical_lookup/prefix 匹配
    add_user_dict: "^``[A-Za-z/`']*$"      #自造词
    unicode: "^U[a-f0-9]+"              # 脚本将自动获取第 2 个字符 U 作为触发前缀，响应 lua_translator@unicode，输出 Unicode 字符
    number: "^R[0-9]+[.]?[0-9]*"        # 脚本将自动获取第 2 个字符 R 作为触发前缀，响应 lua_translator@number_translator，数字金额大写
    gregorian_to_lunar: "^N[0-9]{1,8}"  # 脚本将自动获取第 2 个字符 N 作为触发前缀，响应 lua_translator@lunar，公历转农历，输入 N20240115 得到「二〇二三年腊月初五」
    calculator: "^V.*$"                 #计算器功能引导，，，，
    quick_symbol: "^;.*$"  # 快符引导，例如输入;q 后自动上屏快速符号，双击;;上屏中文分号本身，如果你想使用顿号 可以改成"^\\\\.*$"，双击输出、而;'输出重复上屏内容

# 标点符号
# punctuator 下面有三个子项：
# 设置为一个映射，就自动上屏；设置为多个映射，如 '/' : [ '/', ÷ ] 则进行复选。
#   full_shape: 全角没改，使用预设值
#   half_shape: 标点符号全部直接上屏，和 macOS 自带输入法的区别是
#              '|' 是半角的，
#              '~' 是半角的，
#              '`'（反引号）没有改成 '·'（间隔号）。
#   symbols    Rime 的预设配置是以 '/' 前缀开头输出一系列字符，自定义的修改 symbols.yaml
punctuator:
  digit_separators: ":,."  #数字分隔符
  __include: wanxiang_symbols:/symbol_table         # 从 symbols.yaml 导入配置

# 从 default 继承快捷键
key_binder:
  import_preset: default  # 从 default.yaml 继承通用的
  sequence: # Lua 配置：手动排序的快捷键 super_sequence.lua，不要用方向键，各种冲突，一定要避免冲突
    up: "Control+j"    # 上移
    down: "Control+k"  # 下移
    reset: "Control+l" # 重置
    pin: "Control+p"   # 置顶
  # Lua 配置: shijian.lua 的引导符，涉及：日期、时间、节日、节气、生日、问候模板等功能
  shijian_keys: ["/", "o"]
  # Lua 配置: 超级tips上屏按键
  tips_key: "period"   #修改时候去default找
  search: "`"             # 辅码引导符，要添加到 speller/alphabet
  bindings:             # 也可以再增加方案专有的
    #- { when: composing, accept: Tab, send: '[' } ## 取消注释后：tab引导辅助码
    - { when: composing, accept: Control+w, send: Control+BackSpace }
#分号用于次选，微软、搜狗双拼不可启用
    #- { when: has_menu, accept: semicolon, send: 2 }
#使用Control+e进入翻译模式
    - { when: has_menu, accept: "Control+e", toggle: chinese_english }
#使用快捷键Control+c拆分显示
    - { when: has_menu, accept: "Control+c", toggle: chaifen_switch }
#使用快捷键Control+a开启和关闭辅助码显示
    - { when: has_menu, accept: "Control+a", toggle: fuzhu_hint }
#通过快捷键Control+s使得输入码显示音调
    - { when: has_menu, accept: "Control+s", toggle: tone_display }
#通过快捷键Control+t开启超级tips
    - { when: has_menu, accept: "Control+t", toggle: super_tips }
#通过快捷键Control+g开启字符集过滤
    - { when: has_menu, accept: "Control+g", toggle: charset_filter }
# 使用 tab 在不同音节之间跳转
    - { when: has_menu, accept: "Tab", send: "Control+Right" }
    - { when: composing, accept: "Tab", send: "Control+Right" }
#当tab第一个字补码正确后，可以使用Ctrl+tab进行上屏并依次补码
    - { when: composing, accept: "Control+Tab", send_sequence: '{Home}{Shift+Right}{1}{Shift+Right}' }
#启用光标回退至特定音节后、补充辅助码的功能。补充完辅助码后，可以再按一次tab回到整句检查
    - { when: composing, accept: Control+1, send_sequence: '{Home}{Shift+Right}' }
    - { when: composing, accept: Control+2, send_sequence: '{Home}{Shift+Right}{Shift+Right}' }
    - { when: composing, accept: Control+3, send_sequence: '{Home}{Shift+Right}{Shift+Right}{Shift+Right}' }
    - { when: composing, accept: Control+4, send_sequence: '{Home}{Shift+Right}{Shift+Right}{Shift+Right}{Shift+Right}' }
    - { when: composing, accept: Control+5, send_sequence: '{Home}{Shift+Right}{Shift+Right}{Shift+Right}{Shift+Right}{Shift+Right}' }
    - { when: composing, accept: Control+6, send_sequence: '{Home}{Shift+Right}{Shift+Right}{Shift+Right}{Shift+Right}{Shift+Right}{Shift+Right}' }
    - { when: composing, accept: Control+7, send_sequence: '{Home}{Shift+Right}{Shift+Right}{Shift+Right}{Shift+Right}{Shift+Right}{Shift+Right}{Shift+Right}' }
#当输入编码后发现没有词，则通过双击``进入造词模式而且不需要删除编码，这个功能与``直接引导相呼应相配合
    - { match: "^.*`$", accept: "`", send_sequence: '{BackSpace}{Home}{`}{`}{End}' }
#通过按下/发送/+1节约一个按键，不冲突的时候可以开启
    #- { match: "[a-z]{1,4}", accept: "/",  send_sequence: "/1" }
#① 翻页 , .
    # - { when: paging, accept: comma, send: Page_Up }
    # - { when: has_menu, accept: period, send: Page_Down }
#② 翻页 [ ]
    # - { when: paging, accept: bracketleft, send: Page_Up }
    # - { when: has_menu, accept: bracketright, send: Page_Down }
#③ 翻页 - =
    - { when: has_menu, accept: minus, send: Page_Up }
    - { when: has_menu, accept: equal, send: Page_Down }
# Option/Alt + ←/→ 切换光标至下/上一个拼音
    - { when: composing, accept: Alt+Left, send: Shift+Left }
    - { when: composing, accept: Alt+Right, send: Shift+Right }
    - { when: always, toggle: ascii_punct, accept: Control+Shift+3 }              # 切换中英标点
    - { when: always, toggle: ascii_punct, accept: Control+Shift+numbersign }     # 切换中英标点
    - { when: always, toggle: s2t, accept: Control+Shift+4 }       # 切换简繁
    - { when: always, toggle: s2t, accept: Control+Shift+dollar }  # 切换简繁

editor:
  bindings:
    space: confirm                        # 空格键：上屏候选项
    Return: commit_raw_input              # 回车键：上屏原始输入
    Control+Return: commit_script_text    # Ctrl+回车键：上屏变换后输入（经过 preedit转换的）
    Control+Shift+Return: commit_comment  # Ctrl+Shift+回车键：上屏 comment
    BackSpace: revert                     # 退格键：向前删除（撤消上次输入）
    Delete: delete                        # Delete 键：向后删除
    Control+BackSpace: back_syllable      # Ctrl+退格键：删除一个音节
    Control+Delete: delete_candidate      # Ctrl+Delete键：删除或降权候选项
    Escape: cancel                        # Esc 键：取消输入

# 拼写设定
speller:
# table_translator翻译器，支持自动上屏。例如 “zmhu”可以自动上屏“怎么回事”
#  auto_select: true
#  auto_select_pattern: ^[a-z]+/|^[a-df-zA-DF-Z]\w{3}|^e\w{4}
  # 如果不想让什么标点直接上屏，可以加在 alphabet，或者编辑标点符号为两个及以上的映射
  alphabet: zyxwvutsrqponmlkjihgfedcbaZYXWVUTSRQPONMLKJIHGFEDCBA7890`;/
  # initials 定义仅作为始码的按键，排除 ` 让单个的 ` 可以直接上屏
  initials: zyxwvutsrqponmlkjihgfedcbaZYXWVUTSRQPONMLKJIHGFEDCBA/
  delimiter: " '"  # 第一位<空格>是拼音之间的分隔符；第二位<'>表示可以手动输入单引号来分割拼音。
  algebra:
    __patch:
      - set_shuru_schema     #拼音转双拼码
      - set_fuzhu_schema     #辅助码部分
#      - mohuyin             #模糊音选择性开启
###############################以下是拼写运算规则中全拼转双拼码以及形码的过程，按照不同的方案规则划分段落###########################
全拼: 
  __append: 
    - xform/^([a-z]+)(;.*)$/$1④$2/
    - xform/^(.*)ā(.*)(;.*)$/$1a$2①$3/
    - xform/^(.*)á(.*)(;.*)$/$1a$2②$3/
    - xform/^(.*)ǎ(.*)(;.*)$/$1a$2③$3/
    - xform/^(.*)à(.*)(;.*)$/$1a$2④$3/
    - xform/^(.*)ō(.*)(;.*)$/$1o$2①$3/
    - xform/^(.*)ó(.*)(;.*)$/$1o$2②$3/
    - xform/^(.*)ǒ(.*)(;.*)$/$1o$2③$3/
    - xform/^(.*)ò(.*)(;.*)$/$1o$2④$3/
    - xform/^(.*)ē(.*)(;.*)$/$1e$2①$3/
    - xform/^(.*)é(.*)(;.*)$/$1e$2②$3/
    - xform/^(.*)ě(.*)(;.*)$/$1e$2③$3/
    - xform/^(.*)è(.*)(;.*)$/$1e$2④$3/
    - xform/^(.*)ī(.*)(;.*)$/$1i$2①$3/
    - xform/^(.*)í(.*)(;.*)$/$1i$2②$3/
    - xform/^(.*)ǐ(.*)(;.*)$/$1i$2③$3/
    - xform/^(.*)ì(.*)(;.*)$/$1i$2④$3/
    - xform/^(.*)ū(.*)(;.*)$/$1u$2①$3/
    - xform/^(.*)ú(.*)(;.*)$/$1u$2②$3/
    - xform/^(.*)ǔ(.*)(;.*)$/$1u$2③$3/
    - xform/^(.*)ù(.*)(;.*)$/$1u$2④$3/
    - xform/^(.*)ǖ(.*)(;.*)$/$1v$2①$3/
    - xform/^(.*)ǘ(.*)(;.*)$/$1v$2②$3/
    - xform/^(.*)ǚ(.*)(;.*)$/$1v$2③$3/
    - xform/^(.*)ǜ(.*)(;.*)$/$1v$2④$3/
    - xform/^(.*)ü(.*)$/$1v$2/
    - xform/^(.*)ń(.*)(;.*)$/$1n$2②$3/
    - xform/^(.*)ň(.*)(;.*)$/$1n$2③$3/
    - xform/^(.*)ǹ(.*)(;.*)$/$1n$2④$3/
    - xlit/①②③④/7890
    - derive/^ng(\d)(;.*)$/eng$1$2/
    - xform/^n(\d)(;.*)/en$1$2/

自然码: 
  __append:
    - xform/^([a-z]+)(;.*)$/$1④$2/
    - xform/^(.*)ā(.*)(;.*)$/$1a$2①$3/
    - xform/^(.*)á(.*)(;.*)$/$1a$2②$3/
    - xform/^(.*)ǎ(.*)(;.*)$/$1a$2③$3/
    - xform/^(.*)à(.*)(;.*)$/$1a$2④$3/
    - xform/^(.*)ō(.*)(;.*)$/$1o$2①$3/
    - xform/^(.*)ó(.*)(;.*)$/$1o$2②$3/
    - xform/^(.*)ǒ(.*)(;.*)$/$1o$2③$3/
    - xform/^(.*)ò(.*)(;.*)$/$1o$2④$3/
    - xform/^(.*)ē(.*)(;.*)$/$1e$2①$3/
    - xform/^(.*)é(.*)(;.*)$/$1e$2②$3/
    - xform/^(.*)ě(.*)(;.*)$/$1e$2③$3/
    - xform/^(.*)è(.*)(;.*)$/$1e$2④$3/
    - xform/^(.*)ī(.*)(;.*)$/$1i$2①$3/
    - xform/^(.*)í(.*)(;.*)$/$1i$2②$3/
    - xform/^(.*)ǐ(.*)(;.*)$/$1i$2③$3/
    - xform/^(.*)ì(.*)(;.*)$/$1i$2④$3/
    - xform/^(.*)ū(.*)(;.*)$/$1u$2①$3/
    - xform/^(.*)ú(.*)(;.*)$/$1u$2②$3/
    - xform/^(.*)ǔ(.*)(;.*)$/$1u$2③$3/
    - xform/^(.*)ù(.*)(;.*)$/$1u$2④$3/
    - xform/^(.*)ǖ(.*)(;.*)$/$1v$2①$3/
    - xform/^(.*)ǘ(.*)(;.*)$/$1v$2②$3/
    - xform/^(.*)ǚ(.*)(;.*)$/$1v$2③$3/
    - xform/^(.*)ǜ(.*)(;.*)$/$1v$2④$3/
    - xform/^(.*)ü(.*)$/$1v$2/
    - xform/^(.*)ń(.*)(;.*)$/$1n$2②$3/
    - xform/^(.*)ň(.*)(;.*)$/$1n$2③$3/
    - xform/^(.*)ǹ(.*)(;.*)$/$1n$2④$3/
    - xlit/①②③④/7890
    - derive/^ng(\d)(;.*)$/eng$1$2/
    - xform/^n(\d)(;.*)/en$1$2/
    - derive/^([jqxy])u(\d)(;.*)$/$1v$2$3/
    - derive/^([aoe])([ioun])(\d)(;.*)$/$1$1$2$3$4/
    - xform/^([aoe])(ng)?(\d)(;.*)$/$1$1$2$3$4/
    - xform/^(\w+?)iu(\d)(;.*)$/$1Ⓠ$2$3/
    - xform/^(\w+?)[uv]an(\d)(;.*)$/$1Ⓡ$2$3/
    - xform/^(\w+?)[uv]e(\d)(;.*)$/$1Ⓣ$2$3/
    - xform/^(\w+?)ing(\d)(;.*)$/$1Ⓨ$2$3/
    - xform/^(\w+?)uai(\d)(;.*)$/$1Ⓨ$2$3/
    - xform/^(\w+?)uo(\d)(;.*)$/$1Ⓞ$2$3/
    - xform/^(\w+?)[uv]n(\d)(;.*)$/$1Ⓟ$2$3/
    - xform/^(\w+?)i?ong(\d)(;.*)$/$1Ⓢ$2$3/
    - xform/^(\w+?)[iu]ang(\d)(;.*)$/$1Ⓓ$2$3/
    - xform/^(\w+?)en(\d)(;.*)$/$1Ⓕ$2$3/
    - xform/^(\w+?)eng(\d)(;.*)$/$1Ⓖ$2$3/
    - xform/^(\w+?)ang(\d)(;.*)$/$1Ⓗ$2$3/
    - xform/^(\w+?)ian(\d)(;.*)$/$1Ⓜ$2$3/
    - xform/^(\w+?)an(\d)(;.*)$/$1Ⓙ$2$3/
    - xform/^(\w+?)iao(\d)(;.*)$/$1Ⓒ$2$3/
    - xform/^(\w+?)ao(\d)(;.*)$/$1Ⓚ$2$3/
    - xform/^(\w+?)ai(\d)(;.*)$/$1Ⓛ$2$3/
    - xform/^(\w+?)ei(\d)(;.*)$/$1Ⓩ$2$3/
    - xform/^(\w+?)ie(\d)(;.*)$/$1Ⓧ$2$3/
    - xform/^(\w+?)ui(\d)(;.*)$/$1Ⓥ$2$3/
    - xform/^(\w+?)ou(\d)(;.*)$/$1Ⓑ$2$3/
    - xform/^(\w+?)in(\d)(;.*)$/$1Ⓝ$2$3/
    - xform/^(\w+?)[iu]a(\d)(;.*)$/$1Ⓦ$2$3/
    - xform/^sh/Ⓤ/
    - xform/^ch/Ⓘ/
    - xform/^zh/Ⓥ/
    - xlit/ⓆⓌⓇⓉⓎⓊⒾⓄⓅⓈⒹⒻⒼⒽⓂⒿⒸⓀⓁⓏⓍⓋⒷⓃ/qwrtyuiopsdfghmjcklzxvbn/
自然龙:    #原作者项目地址： https://github.com/rimeinn/rime-zrlong
  __append:
    - xform/^ēr/eQ/
    - xform/^ér/eK/
    - xform/^ěr/eU/
    - xform/^èr/eH/
    - xform/^er/eQ/
    - xform/^(ā|á|ǎ|à)([ioun])/a$1$2/
    - xform/^(ō|ó|ǒ|ò)([ioun])/o$1$2/
    - xform/^(ē|é|ě|è)([ioun])/e$1$2/
    - xform/^(ā|á|ǎ|à)(ng)/a$1$2/
    - xform/^(ō|ó|ǒ|ò)(ng)/o$1$2/
    - xform/^(ē|é|ě|è)(ng)/e$1$2/
    - xform/^(ā|á|ǎ|à)/a$1/
    - xform/^(ō|ó|ǒ|ò)/o$1/
    - xform/^(ē|é|ě|è)/e$1/
    - xform/^([jqxy])u(;.*)/$1ü$2/
    - xform/^([jqxy])ū(;.*)/$1ǖ$2/
    - xform/^([jqxy])ú(;.*)/$1ǘ$2/
    - xform/^([jqxy])ǔ(;.*)/$1ǚ$2/
    - xform/^([jqxy])ù(;.*)/$1ǜ$2/
    - xform/^a(;.*)$/aā$1/
    - xform/^o(;.*)$/oō$1/
    - xform/^e(;.*)$/eē$1/
    - xform/^ǹg/eèng/
    - xform/^ňg/eěng/
    - xform/^ńg/eéng/
    - xform/^ng/eeng/
    - xform/^ǹ/eèn/
    - xform/^ň/eěn/
    - xform/^ń/eén/
    - xform/^n(;.*)/een$1/
    - xform/^sh/U/
    - xform/^ch/I/
    - xform/^zh/V/
    - xform/^(.)(iáo|iǎng|uǎng|ang|āng|ue|uē|üe|ǖe|ǎi|á)(;.*)$/$1U$3/
    - xform/^(.)(iàng|iǒng|uàng|ēn|en|īng|ing|é|ó)(;.*)$/$1E$3/
    - xform/^(.)(iǎn|iōng|iong|uǎi|uò|ǎng|ō|o)(;.*)$/$1P$3/
    - xform/^(.)(uāng|uang|ǐng|ìng|uí|áng)(;.*)$/$1W$3/
    - xform/^(.)(uǎn|uái||uā|ua|én|uō|uo|ié|ǚ)(;.*)$/$1S$3/
    - xform/^(.)(uán|ài|ěn|èn|uě|ǚe|ǎn|ǔn|iù)(;.*)$/$1O$3/
    - xform/^(.)(uān|uan|àng|ái|iā|ia|uè|üè)(;.*)$/$1D$3/
    - xform/^(.)(iáng|áo|ué|üé|ēi|ei|à|è|ǒ)(;.*)$/$1I$3/
    - xform/^(.)(uāi|uai|uà|uǎ|ūn|un|ò|ǐ)(;.*)$/$1G$3/
    - xform/^(.)(éng|èng|uài|èi|uì|ǜ|ún)(;.*)$/$1F$3/
    - xform/^(.)(ióng|ōng|ong|án|iē|ie)(;.*)$/$1K$3/
    - xform/^(.)(iào|iǎo|uǒ|uó|a|ā|ě|ú)(;.*)$/$1L$3/
    - xform/^(.)(uàn|ēng|eng|iá|ín|iě)(;.*)$/$1C$3/
    - xform/^(.)(iān|ian|òu|éi|ùn|ē|e)(;.*)$/$1R$3/
    - xform/^(.)(iāng|iang|ěng|òng)(;.*)$/$1Y$3/
    - xform/^(.)(iao|iāo|ǔ|ǎ|iú|ǘ)(;.*)$/$1M$3/
    - xform/^(.)(iǎ|íng|ān|an|ǒng)(;.*)$/$1N$3/
    - xform/^(.)(iòng|īn|in|ǖ|ü|ù)(;.*)$/$1H$3/
    - xform/^(.)(ǎo|ià|ǐn|ōu|ou)(;.*)$/$1X$3/
    - xform/^(.)(óng|àn|ěi|ī|i)(;.*)$/$1J$3/
    - xform/^(.)(ián|ào|ìn|uǐ)(;.*)$/$1V$3/
    - xform/^(.)(uáng|āi|ai|í)(;.*)$/$1B$3/
    - xform/^(.)(ǒu|iū|iu|iǔ)(;.*)$/$1Z$3/
    - xform/^(.)(uá|uī|ui|ì)(;.*)$/$1T$3/
    - xform/^(.)(ū|u|óu|iàn)(;.*)$/$1A$3/
    - xform/^(.)(āo|ao|iè)(;.*)$/$1Q$3/
    - xlit/QWERTYUIOPASDFGHMJCKLZXVBN/qwertyuiopasdfghmjcklzxvbn/
汉心龙:    #原作者项目地址： https://hanxinma.gitlab.io/longma/
  __append:
    - xform/^(a|ā|á|ǎ|à)/Q$1/
    - xform/^(o|ō|ó|ǒ|ò)/R$1/
    - xform/^(e|ē|é|ě|è)/B$1/
    - xform/^([jqxy])u(;.*)/$1ü$2/
    - xform/^([jqxy])ū(;.*)/$1ǖ$2/
    - xform/^([jqxy])ú(;.*)/$1ǘ$2/
    - xform/^([jqxy])ǔ(;.*)/$1ǚ$2/
    - xform/^([jqxy])ù(;.*)/$1ǜ$2/
    - xform/^ǹg/Bèng/
    - xform/^ňg/Běng/
    - xform/^ńg/Béng/
    - xform/^ng/beng/
    - xform/^ǹ/Bèn/
    - xform/^ň/Běn/
    - xform/^ń/Bén/
    - xform/^n(;.*)/Ben$1/
    - xform/^sh/T/
    - xform/^ch/S/
    - xform/^zh/K/
    - xform/^a/Q/
    - xform/^p/W/
    - xform/^j/E/
    - xform/^o/R/
    - xform/^n/Y/
    - xform/^k/U/
    - xform/^b/I/
    - xform/^t/O/
    - xform/^m/P/
    - xform/^q/A/
    - xform/^l/D/
    - xform/^r/F/
    - xform/^w/G/
    - xform/^d/H/
    - xform/^y/J/
    - xform/^g/L/
    - xform/^s/Z/
    - xform/^c/X/
    - xform/^x/C/
    - xform/^f/V/
    - xform/^e/B/
    - xform/^z/N/
    - xform/^h/M/
    - xform/^(.)(ēn|en|iǎn|ǒng|iáng|uá|ó|án|ǐng)(;.*)$/$1Q$3/
    - xform/^(.)(ào|èn|èng|iě|iàng)(;.*)$/$1W$3/
    - xform/^(.)(ü|ǖ|iù|ā|a|uì|uǎng)(;.*)$/$1E$3/
    - xform/^(.)(ìn|è|à|ě|ǎi|áo)(;.*)$/$1R$3/
    - xform/^(.)(ō|o|ìòng|ǎo|óu)(;.*)$/$1T$3/
    - xform/^(.)(én|ià|ang|āng|iú|ǐn|üē|uē|üe|ue)(;.*)$/$1Y$3/
    - xform/^(.)(àng|iàn|ao|āo)(;.*)$/$1U$3/
    - xform/^(.)(ù|uán|ué|üé)(;.*)$/$1I$3/
    - xform/^(.)(éng|iā|ia|ān|an|èr|uè|üè|iu|iū|uài|iáo)(;.*)$/$1O$3/
    - xform/^(.)(uǒ|iè|iào|ěr|ǎng)(;.*)$/$1P$3/
    - xform/^(.)(āi|ai|óng|ìng|uāng|uang|á|ēr|er|iǎ)(;.*)$/$1A$3/
    - xform/^(.)(uō|uo|ǚ|uó|àn|ín|ūn|un|iōng|iong)(;.*)$/$1S$3/
    - xform/^(.)(ǒu|èi|é|iāo|iao|iá)(;.*)$/$1D$3/
    - xform/^(.)(ǐ|uān|uan|uà|ò|uǎ)(;.*)$/$1F$3/
    - xform/^(.)(ī|i|ěn|ěi|uáng)(;.*)$/$1G$3/
    - xform/^(.)(ǜ|ēng|eng|iē|ie|éi|ún|uāi|uai)(;.*)$/$1H$3/
    - xform/^(.)(ái|ǒ|ū|u|ér|īng|ing|uǎi)(;.*)$/$1J$3/
    - xform/^(.)(ē|e|íng|òu|ié)(;.*)$/$1K$3/
    - xform/^(.)(ì|uī|ui)(;.*)$/$1L$3/
    - xform/^(.)(uā|ua|òng|uǐ|áng|iǔ|iǎo)(;.*)$/$1Z$3/
    - xform/^(.)(uò|ōu|ou|iān|ian|ēi|ei|ùn)(;.*)$/$1X$3/
    - xform/^(.)(ǔ|uàn)(;.*)$/$1C$3/
    - xform/^(.)(ǎ|uái|ōng|ong|īn|in|ióng|iǒng)(;.*)$/$1V$3/
    - xform/^(.)(ài|ián|üě|uě|uí|uǎn|uàng|ǘ)(;.*)$/$1B$3/
    - xform/^(.)(í|iǎng|ǎn)(;.*)$/$1N$3/
    - xform/^(.)(ú|iāng|iang|ěng|ǔn)(;.*)$/$1M$3/
    - xlit/QWERTYUIOPASDFGHMJCKLZXVBN/qwertyuiopasdfghmjcklzxvbn/

龙三:
  __append:
    - xform/^ēr/e¥/
    - xform/^ér/e¥/
    - xform/^ěr/e¥/
    - xform/^èr/e&/
    - xform/^er/e&/
    - xform/^(ā|á|ǎ|à)([ioun])/a$1$2/
    - xform/^(ō|ó|ǒ|ò)([ioun])/o$1$2/
    - xform/^(ē|é|ě|è)([ioun])/e$1$2/
    - xform/^(ā|á|ǎ|à)(ng)/a$1$2/
    - xform/^(ō|ó|ǒ|ò)(ng)/o$1$2/
    - xform/^(ē|é|ě|è)(ng)/e$1$2/
    - xform/^(ā|á|ǎ|à)/a$1/
    - xform/^(ō|ó|ǒ|ò)/o$1/
    - xform/^(ē|é|ě|è)/e$1/
    - xform/^([jqxy])u(;.*)/$1ü$2/
    - xform/^([jqxy])ū(;.*)/$1ǖ$2/
    - xform/^([jqxy])ú(;.*)/$1ǘ$2/
    - xform/^([jqxy])ǔ(;.*)/$1ǚ$2/
    - xform/^([jqxy])ù(;.*)/$1ǜ$2/
    - xform/^a(;.*)$/aā$1/
    - xform/^o(;.*)$/oō$1/
    - xform/^e(;.*)$/eē$1/
    - xform/^ǹg/nèng/
    - xform/^ňg/něng/
    - xform/^ńg/néng/
    - xform/^ng/neng/
    - xform/^ǹ/eèn/
    - xform/^ň/eěn/
    - xform/^ń/eén/
    - xform/^n(;.*)/een$1/
    - xform/^sh/U/
    - xform/^ch/I/
    - xform/^zh/V/
    - xform/^y/E/
    - xform/^p/Y/
    - xform/^e/P/
    - xform/^(.)(āo|àng|ang|ióng|iǒng|iú|iǔ|iù|iu|uǎ|uá)(;.*)$/$1Q$3/
    - xform/^(.)(iā|uàng|uang|ó|ǒ|òu|ou|ún|ǔn)(;.*)$/$1W$3/
    - xform/^(.)(iān|é|ě)(;.*)$/$1E$3/
    - xform/^(.)(|án|ǎn|iào|iao)(;.*)$/$1R$3/
    - xform/^(.)(áng|ǎng|ián|iǎn|uài|uai)(;.*)$/$1T$3/
    - xform/^(.)(ìng|ing|ú|ǔ)(;.*)$/$1Y$3/
    - xform/^(.)(è|e|ēi|iē|iū)(;.*)$/$1U$3/
    - xform/^(.)(ā|ào|uī|ǖ|iàng|iang)(;.*)$/$1I$3/
    - xform/^(.)(í|ǐ|uó|uǒ)(;.*)$/$1O$3/
    - xform/^(.)(iāng|ù|u|ǘ|ǚ)(;.*)$/$1P$3/
    - xform/^(.)(ì|i|uà|ua)(;.*)$/$1A$3/
    - xform/^(.)(àn|an|iáng|iǎng|iòng|iong|uǐ|uí|ūn)(;.*)$/$1S$3/
    - xform/^(.)(éng|ěng|īng|uā|ùn|un)(;.*)$/$1D$3/
    - xform/^(.)(āi|ēng|iá|iǎ|ín|ǐn|iōng|uàn|uan|ér|ěr)(;.*)$/$1F$3/
    - xform/^(.)(íng|ǐng|ū)(;.*)$/$1G$3/
    - xform/^(.)(ài|ai|iāo|uán|uǎn)(;.*)$/$1H$3/
    - xform/^(.)(én|ěn|òng|ong|uò|uo|üé|ǚe|ué|uě)(;.*)$/$1J$3/
    - xform/^(.)(èi|ei|ōng|in|ìn|uō|üè|üe|uè|ue)(;.*)$/$1K$3/
    - xform/^(.)(éi|ěi|ī|uáng|uǎng)(;.*)$/$1L$3/
    - xform/^(.)(áo|ǎo|ié|iě|uāng|ǖe|uē)(;.*)$/$1Z$3/
    - xform/^(.)(āng|iáo|iǎo|ò|o|uái|uǎi)(;.*)$/$1X$3/
    - xform/^(.)(á|ǎ|ái|ǎi|ià|ia|ōu|uāi|èr|er)(;.*)$/$1C$3/
    - xform/^(.)(èng|eng|ō|ǜ|ü|uì|ui)(;.*)$/$1V$3/
    - xform/^(.)(ān|ē|iè|ie)(;.*)$/$1B$3/
    - xform/^(.)(èn|en|iàn|ian|óu|ǒu|uān)(;.*)$/$1N$3/
    - xform/^(.)(à|a|ēn|īn|óng|ǒng)(;.*)$/$1M$3/
    - xlit/QWERTYUIOPASDFGHMJCKLZXVBN¥&/qwertyuiopasdfghmjcklzxvbnfc/
小鹤双拼: 
  __append:
    - xform/^([a-z]+)(;.*)$/$1④$2/
    - xform/^(.*)ā(.*)(;.*)$/$1a$2①$3/
    - xform/^(.*)á(.*)(;.*)$/$1a$2②$3/
    - xform/^(.*)ǎ(.*)(;.*)$/$1a$2③$3/
    - xform/^(.*)à(.*)(;.*)$/$1a$2④$3/
    - xform/^(.*)ō(.*)(;.*)$/$1o$2①$3/
    - xform/^(.*)ó(.*)(;.*)$/$1o$2②$3/
    - xform/^(.*)ǒ(.*)(;.*)$/$1o$2③$3/
    - xform/^(.*)ò(.*)(;.*)$/$1o$2④$3/
    - xform/^(.*)ē(.*)(;.*)$/$1e$2①$3/
    - xform/^(.*)é(.*)(;.*)$/$1e$2②$3/
    - xform/^(.*)ě(.*)(;.*)$/$1e$2③$3/
    - xform/^(.*)è(.*)(;.*)$/$1e$2④$3/
    - xform/^(.*)ī(.*)(;.*)$/$1i$2①$3/
    - xform/^(.*)í(.*)(;.*)$/$1i$2②$3/
    - xform/^(.*)ǐ(.*)(;.*)$/$1i$2③$3/
    - xform/^(.*)ì(.*)(;.*)$/$1i$2④$3/
    - xform/^(.*)ū(.*)(;.*)$/$1u$2①$3/
    - xform/^(.*)ú(.*)(;.*)$/$1u$2②$3/
    - xform/^(.*)ǔ(.*)(;.*)$/$1u$2③$3/
    - xform/^(.*)ù(.*)(;.*)$/$1u$2④$3/
    - xform/^(.*)ǖ(.*)(;.*)$/$1v$2①$3/
    - xform/^(.*)ǘ(.*)(;.*)$/$1v$2②$3/
    - xform/^(.*)ǚ(.*)(;.*)$/$1v$2③$3/
    - xform/^(.*)ǜ(.*)(;.*)$/$1v$2④$3/
    - xform/^(.*)ü(.*)$/$1v$2/
    - xform/^(.*)ń(.*)(;.*)$/$1n$2②$3/
    - xform/^(.*)ň(.*)(;.*)$/$1n$2③$3/
    - xform/^(.*)ǹ(.*)(;.*)$/$1n$2④$3/
    - xlit/①②③④/7890
    - derive/^ng(\d)(;.*)$/eng$1$2/
    - xform/^n(\d)(;.*)/en$1$2/
    - derive/^([jqxy])u(\d)(;.*)$/$1v$2$3/
    - derive/^([aoe])([ioun])(\d)(;.*)$/$1$1$2$3$4/
    - xform/^([aoe])(ng)?(\d)(;.*)$/$1$1$2$3$4/
    - xform/^(\w+?)iu(\d)(;.*)/$1Ⓠ$2$3/
    - xform/^(\w+?)ei(\d)(;.*)/$1Ⓦ$2$3/
    - xform/^(\w+?)uan(\d)(;.*)/$1Ⓡ$2$3/
    - xform/^(\w+?)[uv]e(\d)(;.*)/$1Ⓣ$2$3/
    - xform/^(\w+?)un(\d)(;.*)/$1Ⓨ$2$3/
    - xform/^(\w+?)uo(\d)(;.*)/$1Ⓞ$2$3/
    - xform/^(\w+?)ie(\d)(;.*)/$1Ⓟ$2$3/
    - xform/^(\w+?)i?ong(\d)(;.*)/$1Ⓢ$2$3/
    - xform/^(\w+?)ing(\d)(;.*)/$1Ⓚ$2$3/
    - xform/^(\w+?)uai(\d)(;.*)/$1Ⓚ$2$3/
    - xform/^(\w+?)ai(\d)(;.*)/$1Ⓓ$2$3/
    - xform/^(\w+?)eng(\d)(;.*)/$1Ⓖ$2$3/
    - xform/^(\w+?)en(\d)(;.*)/$1Ⓕ$2$3/
    - xform/^(\w+?)[iu]ang(\d)(;.*)/$1Ⓛ$2$3/
    - xform/^(\w+?)ang(\d)(;.*)/$1Ⓗ$2$3/
    - xform/^(\w+?)ian(\d)(;.*)/$1Ⓜ$2$3/
    - xform/^(\w+?)an(\d)(;.*)/$1Ⓙ$2$3/
    - xform/^(\w+?)ou(\d)(;.*)/$1Ⓩ$2$3/
    - xform/^(\w+?)iao(\d)(;.*)/$1Ⓝ$2$3/
    - xform/^(\w+?)[iu]a(\d)(;.*)/$1Ⓧ$2$3/
    - xform/^(\w+?)ao(\d)(;.*)/$1Ⓒ$2$3/
    - xform/^(\w+?)ui(\d)(;.*)/$1Ⓥ$2$3/
    - xform/^(\w+?)in(\d)(;.*)/$1Ⓑ$2$3/
    - xform/^sh/Ⓤ/
    - xform/^ch/Ⓘ/
    - xform/^zh/Ⓥ/
    - xlit/ⓆⓌⓇⓉⓎⓊⒾⓄⓅⓈⒹⒻⒼⒽⒿⓀⓁⓏⓍⒸⓋⒷⓃⓂ/qwrtyuiopsdfghjklzxcvbnm/

微软双拼: 
  __append:
    - xform/^([a-z]+)(;.*)$/$1④$2/
    - xform/^(.*)ā(.*)(;.*)$/$1a$2①$3/
    - xform/^(.*)á(.*)(;.*)$/$1a$2②$3/
    - xform/^(.*)ǎ(.*)(;.*)$/$1a$2③$3/
    - xform/^(.*)à(.*)(;.*)$/$1a$2④$3/
    - xform/^(.*)ō(.*)(;.*)$/$1o$2①$3/
    - xform/^(.*)ó(.*)(;.*)$/$1o$2②$3/
    - xform/^(.*)ǒ(.*)(;.*)$/$1o$2③$3/
    - xform/^(.*)ò(.*)(;.*)$/$1o$2④$3/
    - xform/^(.*)ē(.*)(;.*)$/$1e$2①$3/
    - xform/^(.*)é(.*)(;.*)$/$1e$2②$3/
    - xform/^(.*)ě(.*)(;.*)$/$1e$2③$3/
    - xform/^(.*)è(.*)(;.*)$/$1e$2④$3/
    - xform/^(.*)ī(.*)(;.*)$/$1i$2①$3/
    - xform/^(.*)í(.*)(;.*)$/$1i$2②$3/
    - xform/^(.*)ǐ(.*)(;.*)$/$1i$2③$3/
    - xform/^(.*)ì(.*)(;.*)$/$1i$2④$3/
    - xform/^(.*)ū(.*)(;.*)$/$1u$2①$3/
    - xform/^(.*)ú(.*)(;.*)$/$1u$2②$3/
    - xform/^(.*)ǔ(.*)(;.*)$/$1u$2③$3/
    - xform/^(.*)ù(.*)(;.*)$/$1u$2④$3/
    - xform/^(.*)ǖ(.*)(;.*)$/$1v$2①$3/
    - xform/^(.*)ǘ(.*)(;.*)$/$1v$2②$3/
    - xform/^(.*)ǚ(.*)(;.*)$/$1v$2③$3/
    - xform/^(.*)ǜ(.*)(;.*)$/$1v$2④$3/
    - xform/^(.*)ü(.*)$/$1v$2/
    - xform/^(.*)ń(.*)(;.*)$/$1n$2②$3/
    - xform/^(.*)ň(.*)(;.*)$/$1n$2③$3/
    - xform/^(.*)ǹ(.*)(;.*)$/$1n$2④$3/
    - xlit/①②③④/7890
    - derive/^ng(\d)(;.*)$/eng$1$2/
    - xform/^n(\d)(;.*)/en$1$2/
    - derive/^([jqxy])u(\d)(;.*)$/$1v$2$3/
    - derive/^([aoe].*)(\d)(;.*)$/o$1$2$3/
    - xform/^([ae])(.*)(\d)(;.*)$/$1$1$2$3$3/
    - xform/^(\w+?)iu(\d)(;.*)/$1Ⓠ$2$3/
    - xform/^(\w+?)er(\d)(;.*)/$1Ⓡ$2$3/
    - xform/^(\w+?)[uv]an(\d)(;.*)/$1Ⓡ$2$3/
    - xform/^(\w+?)[uv]e(\d)(;.*)/$1Ⓣ$2$3/
    - xform/^(\w+?)v(\d)(;.*)/$1Ⓨ$2$3/
    - xform/^(\w+?)uai(\d)(;.*)/$1Ⓨ$2$3/
    - xform/^(\w+?)uo(\d)(;.*)/$1Ⓞ$2$3/
    - xform/^(\w+?)[uv]n(\d)(;.*)/$1Ⓟ$2$3/
    - xform/^(\w+?)i?ong(\d)(;.*)/$1Ⓢ$2$3/
    - xform/^(\w+?)[iu]ang(\d)(;.*)/$1Ⓓ$2$3/
    - xform/^(\w+?)eng(\d)(;.*)/$1Ⓖ$2$3/
    - xform/^(\w+?)en(\d)(;.*)/$1Ⓕ$2$3/
    - xform/^(\w+?)ang(\d)(;.*)/$1Ⓗ$2$3/
    - xform/^(\w+?)ian(\d)(;.*)/$1Ⓜ$2$3/
    - xform/^(\w+?)an(\d)(;.*)/$1Ⓙ$2$3/
    - xform/^(\w+?)iao(\d)(;.*)/$1Ⓒ$2$3/
    - xform/^(\w+?)ao(\d)(;.*)/$1Ⓚ$2$3/
    - xform/^(\w+?)ai(\d)(;.*)/$1Ⓛ$2$3/
    - xform/^(\w+?)ei(\d)(;.*)/$1Ⓩ$2$3/
    - xform/^(\w+?)ie(\d)(;.*)/$1Ⓧ$2$3/
    - xform/^(\w+?)ui(\d)(;.*)/$1Ⓥ$2$3/
    - derive/Ⓣ/Ⓥ/
    - xform/^(\w+?)ou(\d)(;.*)/$1Ⓑ$2$3/
    - xform/^(\w+?)ing(\d)(;.*)/$1◯$2$3/  #注意这里的◯用来暂时替换分号
    - xform/^(\w+?)in(\d)(;.*)/$1Ⓝ$2$3/
    - xform/^(\w+?)[iu]a(\d)(;.*)/$1Ⓦ$2$3/
    - xform/^sh/Ⓤ/
    - xform/^ch/Ⓘ/
    - xform/^zh/Ⓥ/
    - xlit/ⓆⓌⓇⓉⓎⓊⒾⓄⓅⓈⒹⒻⒼⒽⓂⒿⒸⓀⓁⓏⓍⓋⒷⓃ/qwrtyuiopsdfghmjcklzxvbn/
搜狗双拼: 
  __append:
    - xform/^([a-z]+)(;.*)$/$1④$2/
    - xform/^(.*)ā(.*)(;.*)$/$1a$2①$3/
    - xform/^(.*)á(.*)(;.*)$/$1a$2②$3/
    - xform/^(.*)ǎ(.*)(;.*)$/$1a$2③$3/
    - xform/^(.*)à(.*)(;.*)$/$1a$2④$3/
    - xform/^(.*)ō(.*)(;.*)$/$1o$2①$3/
    - xform/^(.*)ó(.*)(;.*)$/$1o$2②$3/
    - xform/^(.*)ǒ(.*)(;.*)$/$1o$2③$3/
    - xform/^(.*)ò(.*)(;.*)$/$1o$2④$3/
    - xform/^(.*)ē(.*)(;.*)$/$1e$2①$3/
    - xform/^(.*)é(.*)(;.*)$/$1e$2②$3/
    - xform/^(.*)ě(.*)(;.*)$/$1e$2③$3/
    - xform/^(.*)è(.*)(;.*)$/$1e$2④$3/
    - xform/^(.*)ī(.*)(;.*)$/$1i$2①$3/
    - xform/^(.*)í(.*)(;.*)$/$1i$2②$3/
    - xform/^(.*)ǐ(.*)(;.*)$/$1i$2③$3/
    - xform/^(.*)ì(.*)(;.*)$/$1i$2④$3/
    - xform/^(.*)ū(.*)(;.*)$/$1u$2①$3/
    - xform/^(.*)ú(.*)(;.*)$/$1u$2②$3/
    - xform/^(.*)ǔ(.*)(;.*)$/$1u$2③$3/
    - xform/^(.*)ù(.*)(;.*)$/$1u$2④$3/
    - xform/^(.*)ǖ(.*)(;.*)$/$1v$2①$3/
    - xform/^(.*)ǘ(.*)(;.*)$/$1v$2②$3/
    - xform/^(.*)ǚ(.*)(;.*)$/$1v$2③$3/
    - xform/^(.*)ǜ(.*)(;.*)$/$1v$2④$3/
    - xform/^(.*)ü(.*)$/$1v$2/
    - xform/^(.*)ń(.*)(;.*)$/$1n$2②$3/
    - xform/^(.*)ň(.*)(;.*)$/$1n$2③$3/
    - xform/^(.*)ǹ(.*)(;.*)$/$1n$2④$3/
    - xlit/①②③④/7890
    - derive/^ng(\d)(;.*)$/eng$1$2/
    - xform/^n(\d)(;.*)/en$1$2/
    - derive/^([jqxy])u(\d)(;.*)$/$1v$2$3/
    - derive/^([aoe].*)(\d)(;.*)$/o$1$2$3/
    - xform/^([ae])(.*)(\d)(;.*)$/$1$1$2$3$4/
    - xform/^(\w+?)iu(\d)(;.*)$/$1Ⓠ$2$3/
    - xform/^(\w+?)[iu]a(\d)(;.*)$/$1Ⓦ$2$3/
    - xform/^(\w+?)er(\d)(;.*)$/$1Ⓡ$2$3/
    - xform/^(\w+?)[uv]an(\d)(;.*)$/$1Ⓡ$2$3/
    - xform/^(\w+?)[uv]e(\d)(;.*)$/$1Ⓣ$2$3/
    - xform/^(\w+?)v(\d)(;.*)$/$1Ⓨ$2$3/
    - xform/^(\w+?)uai(\d)(;.*)$/$1Ⓨ$2$3/
    - xform/^(\w+?)uo(\d)(;.*)$/$1Ⓞ$2$3/
    - xform/^(\w+?)[uv]n(\d)(;.*)$/$1Ⓟ$2$3/
    - xform/^(\w+?)i?ong(\d)(;.*)$/$1Ⓢ$2$3/
    - xform/^(\w+?)[iu]ang(\d)(;.*)$/$1Ⓓ$2$3/
    - xform/^(\w+?)en(\d)(;.*)$/$1Ⓕ$2$3/
    - xform/^(\w+?)eng(\d)(;.*)$/$1Ⓖ$2$3/
    - xform/^(\w+?)ang(\d)(;.*)$/$1Ⓗ$2$3/
    - xform/^(\w+?)ian(\d)(;.*)$/$1Ⓜ$2$3/
    - xform/^(\w+?)an(\d)(;.*)$/$1Ⓙ$2$3/
    - xform/^(\w+?)iao(\d)(;.*)$/$1Ⓒ$2$3/
    - xform/^(\w+?)ao(\d)(;.*)$/$1Ⓚ$2$3/
    - xform/^(\w+?)ai(\d)(;.*)$/$1Ⓛ$2$3/
    - xform/^(\w+?)ei(\d)(;.*)$/$1Ⓩ$2$3/
    - xform/^(\w+?)ie(\d)(;.*)$/$1Ⓧ$2$3/
    - xform/^(\w+?)ui(\d)(;.*)$/$1Ⓥ$2$3/
    - xform/^(\w+?)ou(\d)(;.*)$/$1Ⓑ$2$3/
    - xform/^(\w+?)in(\d)(;.*)$/$1Ⓝ$2$3/
    - xform/^(\w+?)ing(\d)(;.*)$/$1◯$2$3/  #注意这里的◯用来暂时替换分号
    - xform/^sh/Ⓤ/
    - xform/^ch/Ⓘ/
    - xform/^zh/Ⓥ/
    - xlit/ⓆⓌⓇⓉⓎⓊⒾⓄⓅⓈⒹⒻⒼⒽⓂⒿⒸⓀⓁⓏⓍⓋⒷⓃ/qwrtyuiopsdfghmjcklzxvbn/

紫光双拼: 
  __append:
    - xform/^([a-z]+)(;.*)$/$1④$2/
    - xform/^(.*)ā(.*)(;.*)$/$1a$2①$3/
    - xform/^(.*)á(.*)(;.*)$/$1a$2②$3/
    - xform/^(.*)ǎ(.*)(;.*)$/$1a$2③$3/
    - xform/^(.*)à(.*)(;.*)$/$1a$2④$3/
    - xform/^(.*)ō(.*)(;.*)$/$1o$2①$3/
    - xform/^(.*)ó(.*)(;.*)$/$1o$2②$3/
    - xform/^(.*)ǒ(.*)(;.*)$/$1o$2③$3/
    - xform/^(.*)ò(.*)(;.*)$/$1o$2④$3/
    - xform/^(.*)ē(.*)(;.*)$/$1e$2①$3/
    - xform/^(.*)é(.*)(;.*)$/$1e$2②$3/
    - xform/^(.*)ě(.*)(;.*)$/$1e$2③$3/
    - xform/^(.*)è(.*)(;.*)$/$1e$2④$3/
    - xform/^(.*)ī(.*)(;.*)$/$1i$2①$3/
    - xform/^(.*)í(.*)(;.*)$/$1i$2②$3/
    - xform/^(.*)ǐ(.*)(;.*)$/$1i$2③$3/
    - xform/^(.*)ì(.*)(;.*)$/$1i$2④$3/
    - xform/^(.*)ū(.*)(;.*)$/$1u$2①$3/
    - xform/^(.*)ú(.*)(;.*)$/$1u$2②$3/
    - xform/^(.*)ǔ(.*)(;.*)$/$1u$2③$3/
    - xform/^(.*)ù(.*)(;.*)$/$1u$2④$3/
    - xform/^(.*)ǖ(.*)(;.*)$/$1v$2①$3/
    - xform/^(.*)ǘ(.*)(;.*)$/$1v$2②$3/
    - xform/^(.*)ǚ(.*)(;.*)$/$1v$2③$3/
    - xform/^(.*)ǜ(.*)(;.*)$/$1v$2④$3/
    - xform/^(.*)ü(.*)$/$1v$2/
    - xform/^(.*)ń(.*)(;.*)$/$1n$2②$3/
    - xform/^(.*)ň(.*)(;.*)$/$1n$2③$3/
    - xform/^(.*)ǹ(.*)(;.*)$/$1n$2④$3/
    - xlit/①②③④/7890
    - derive/^ng(\d)(;.*)$/eng$1$2/
    - xform/^n(\d)(;.*)/en$1$2/
    - derive/^([jqxy])u(\d)(;.*)$/$1v$2$3/
    - xform/^([aoe].*)(\d)(;.*)$/o$1$2$3/
    - xform/^(\w+?)en(\d)(;.*)$/$1Ⓦ$2$3/
    - xform/^(\w+?)eng(\d)(;.*)$/$1Ⓣ$2$3/
    - xform/^(\w+?)in(\d)(;.*)$/$1Ⓨ$2$3/
    - xform/^(\w+?)uai(\d)(;.*)$/$1Ⓨ$2$3/
    - xform/^(\w+?)uo(\d)(;.*)$/$1Ⓞ$2$3/
    - xform/^(\w+?)ai(\d)(;.*)$/$1Ⓟ$2$3/
    - xform/^(\w+?)[iu]ang(\d)(;.*)$/$1Ⓖ$2$3/
    - xform/^(\w+?)ang(\d)(;.*)$/$1Ⓢ$2$3/
    - xform/^(\w+?)ie(\d)(;.*)$/$1Ⓓ$2$3/
    - xform/^(\w+?)ian(\d)(;.*)$/$1Ⓕ$2$3/
    - xform/^(\w+?)i?ong(\d)(;.*)$/$1Ⓗ$2$3/
    - xform/^(\w+?)er(\d)(;.*)$/$1Ⓙ$2$3/
    - xform/^(\w+?)iu(\d)(;.*)$/$1Ⓙ$2$3/
    - xform/^(\w+?)ei(\d)(;.*)$/$1Ⓚ$2$3/
    - xform/^(\w+?)uan(\d)(;.*)$/$1Ⓛ$2$3/
    - xform/^(\w+?)ing(\d)(;.*)$/$1◯$2$3/    #注意这里的◯用来暂时替换分号
    - xform/^(\w+?)ou(\d)(;.*)$/$1Ⓩ$2$3/
    - xform/^(\w+?)[iu]a(\d)(;.*)$/$1Ⓧ$2$3/
    - xform/^(\w+?)iao(\d)(;.*)$/$1Ⓑ$2$3/
    - xform/^(\w+?)ue(\d)(;.*)$/$1Ⓝ$2$3/
    - xform/^(\w+?)ui(\d)(;.*)$/$1Ⓝ$2$3/
    - xform/^(\w+?)ve(\d)(;.*)$/$1Ⓝ$2$3/
    - xform/^(\w+?)un(\d)(;.*)$/$1Ⓜ$2$3/
    - xform/^(\w+?)ao(\d)(;.*)$/$1Ⓠ$2$3/
    - xform/^(\w+?)an(\d)(;.*)$/$1Ⓡ$2$3/
    - xform/^zh/Ⓤ/
    - xform/^sh/Ⓘ/
    - xform/^ch/Ⓐ/
    - xlit/ⓌⓉⓎⓊⒾⓄⓅⒶⒼⓈⒹⒻⒽⒿⓀⓁⓏⓍⒷⓃⓂⓆⓇ/wtyuiopagsdfhjklzxbnmqr/

智能ABC: 
  __append:
    - xform/^([a-z]+)(;.*)$/$1④$2/
    - xform/^(.*)ā(.*)(;.*)$/$1a$2①$3/
    - xform/^(.*)á(.*)(;.*)$/$1a$2②$3/
    - xform/^(.*)ǎ(.*)(;.*)$/$1a$2③$3/
    - xform/^(.*)à(.*)(;.*)$/$1a$2④$3/
    - xform/^(.*)ō(.*)(;.*)$/$1o$2①$3/
    - xform/^(.*)ó(.*)(;.*)$/$1o$2②$3/
    - xform/^(.*)ǒ(.*)(;.*)$/$1o$2③$3/
    - xform/^(.*)ò(.*)(;.*)$/$1o$2④$3/
    - xform/^(.*)ē(.*)(;.*)$/$1e$2①$3/
    - xform/^(.*)é(.*)(;.*)$/$1e$2②$3/
    - xform/^(.*)ě(.*)(;.*)$/$1e$2③$3/
    - xform/^(.*)è(.*)(;.*)$/$1e$2④$3/
    - xform/^(.*)ī(.*)(;.*)$/$1i$2①$3/
    - xform/^(.*)í(.*)(;.*)$/$1i$2②$3/
    - xform/^(.*)ǐ(.*)(;.*)$/$1i$2③$3/
    - xform/^(.*)ì(.*)(;.*)$/$1i$2④$3/
    - xform/^(.*)ū(.*)(;.*)$/$1u$2①$3/
    - xform/^(.*)ú(.*)(;.*)$/$1u$2②$3/
    - xform/^(.*)ǔ(.*)(;.*)$/$1u$2③$3/
    - xform/^(.*)ù(.*)(;.*)$/$1u$2④$3/
    - xform/^(.*)ǖ(.*)(;.*)$/$1v$2①$3/
    - xform/^(.*)ǘ(.*)(;.*)$/$1v$2②$3/
    - xform/^(.*)ǚ(.*)(;.*)$/$1v$2③$3/
    - xform/^(.*)ǜ(.*)(;.*)$/$1v$2④$3/
    - xform/^(.*)ü(.*)$/$1v$2/
    - xform/^(.*)ń(.*)(;.*)$/$1n$2②$3/
    - xform/^(.*)ň(.*)(;.*)$/$1n$2③$3/
    - xform/^(.*)ǹ(.*)(;.*)$/$1n$2④$3/
    - xlit/①②③④/7890
    - derive/^ng(\d)(;.*)$/eng$1$2/
    - xform/^n(\d)(;.*)/en$1$2/
    - xform/^([aoe].*)(\d)(;.*)$/Ⓞ$1$2$3/
    - xform/^(\w+?)ei(\d)(;.*)$/$1Ⓠ$2$3/
    - xform/^(\w+?)ian(\d)(;.*)$/$1Ⓦ$2$3/
    - xform/^(\w+?)er(\d)(;.*)$/$1Ⓡ$2$3/
    - xform/^(\w+?)iu(\d)(;.*)$/$1Ⓡ$2$3/
    - xform/^(\w+?)[iu]ang(\d)(;.*)$/$1Ⓣ$2$3/
    - xform/^(\w+?)ing(\d)(;.*)$/$1Ⓨ$2$3/
    - xform/^(\w+?)uo(\d)(;.*)$/$1Ⓞ$2$3/
    - xform/^(\w+?)uan(\d)(;.*)$/$1Ⓟ$2$3/
    - xform/^(\w+?)i?ong(\d)(;.*)$/$1Ⓢ$2$3/
    - xform/^(\w+?)[iu]a(\d)(;.*)$/$1Ⓓ$2$3/
    - xform/^(\w+?)en(\d)(;.*)$/$1Ⓕ$2$3/
    - xform/^(\w+?)eng(\d)(;.*)$/$1Ⓖ$2$3/
    - xform/^(\w+?)ang(\d)(;.*)$/$1Ⓗ$2$3/
    - xform/^(\w+?)an(\d)(;.*)$/$1Ⓙ$2$3/
    - xform/^(\w+?)iao(\d)(;.*)$/$1Ⓩ$2$3/
    - xform/^(\w+?)ao(\d)(;.*)$/$1Ⓚ$2$3/
    - xform/^(\w+?)in(\d)(;.*)$/$1Ⓒ$2$3/
    - xform/^(\w+?)uai(\d)(;.*)$/$1Ⓒ$2$3/
    - xform/^(\w+?)ai(\d)(;.*)$/$1Ⓛ$2$3/
    - xform/^(\w+?)ie(\d)(;.*)$/$1Ⓧ$2$3/
    - xform/^(\w+?)ou(\d)(;.*)$/$1Ⓑ$2$3/
    - xform/^(\w+?)un(\d)(;.*)$/$1Ⓝ$2$3/
    - xform/^(\w+?)[uv]e(\d)(;.*)$/$1Ⓜ$2$3/
    - xform/^(\w+?)ui(\d)(;.*)$/$1Ⓜ$2$3/
    - xform/^zh/Ⓐ/
    - xform/^ch/Ⓔ/
    - xform/^sh/Ⓥ/
    - xlit/ⓆⓌⒺⓇⓉⓎⓄⓅⒶⓈⒹⒻⒼⒽⒿⓀⓁⓏⓍⒸⓋⒷⓃⓂ/qwertyopasdfghjklzxcvbnm/


国标双拼:
  __append:
    - xform/^([a-z]+)(;.*)$/$1④$2/
    - xform/^(.*)ā(.*)(;.*)$/$1a$2①$3/
    - xform/^(.*)á(.*)(;.*)$/$1a$2②$3/
    - xform/^(.*)ǎ(.*)(;.*)$/$1a$2③$3/
    - xform/^(.*)à(.*)(;.*)$/$1a$2④$3/
    - xform/^(.*)ō(.*)(;.*)$/$1o$2①$3/
    - xform/^(.*)ó(.*)(;.*)$/$1o$2②$3/
    - xform/^(.*)ǒ(.*)(;.*)$/$1o$2③$3/
    - xform/^(.*)ò(.*)(;.*)$/$1o$2④$3/
    - xform/^(.*)ē(.*)(;.*)$/$1e$2①$3/
    - xform/^(.*)é(.*)(;.*)$/$1e$2②$3/
    - xform/^(.*)ě(.*)(;.*)$/$1e$2③$3/
    - xform/^(.*)è(.*)(;.*)$/$1e$2④$3/
    - xform/^(.*)ī(.*)(;.*)$/$1i$2①$3/
    - xform/^(.*)í(.*)(;.*)$/$1i$2②$3/
    - xform/^(.*)ǐ(.*)(;.*)$/$1i$2③$3/
    - xform/^(.*)ì(.*)(;.*)$/$1i$2④$3/
    - xform/^(.*)ū(.*)(;.*)$/$1u$2①$3/
    - xform/^(.*)ú(.*)(;.*)$/$1u$2②$3/
    - xform/^(.*)ǔ(.*)(;.*)$/$1u$2③$3/
    - xform/^(.*)ù(.*)(;.*)$/$1u$2④$3/
    - xform/^(.*)ǖ(.*)(;.*)$/$1v$2①$3/
    - xform/^(.*)ǘ(.*)(;.*)$/$1v$2②$3/
    - xform/^(.*)ǚ(.*)(;.*)$/$1v$2③$3/
    - xform/^(.*)ǜ(.*)(;.*)$/$1v$2④$3/
    - xform/^(.*)ü(.*)$/$1v$2/
    - xform/^(.*)ń(.*)(;.*)$/$1n$2②$3/
    - xform/^(.*)ň(.*)(;.*)$/$1n$2③$3/
    - xform/^(.*)ǹ(.*)(;.*)$/$1n$2④$3/
    - xlit/①②③④/7890
    - derive/^ng(\d)(;.*)$/eng$1$2/
    - xform/^n(\d)(;.*)/en$1$2/
    - derive/^([jqxy])u(\d)(;.*)$/$1v$2$3/
    - derive/^([aoe])([ioun])(\d)(;.*)$/$1$1$2$3$4/
    - xform/^([aoe])(ng)?(\d)(;.*)$/$1$1$2$3$4/
    - xform/^(\w+?)[iu]a(\d)(;.*)$/$1Ⓠ$2$3/
    - xform/^(\w+?)[vu]an(\d)(;.*)$/$1Ⓦ$2$3/
    - xform/^(\w+?)en(\d)(;.*)$/$1Ⓡ$2$3/
    - xform/^(\w+?)ie(\d)(;.*)$/$1Ⓣ$2$3/
    - xform/^(\w+?)(iu|uai)(\d)(;.*)$/$1Ⓨ$3$4/
    - xform/^(\w+?)uo(\d)(;.*)$/$1Ⓞ$2$3/
    - xform/^(\w+?)ou(\d)(;.*)$/$1Ⓟ$2$3/
    - xform/^(\w+?)i?ong(\d)(;.*)$/$1Ⓢ$2$3/
    - xform/^(\w+?)ian(\d)(;.*)$/$1Ⓓ$2$3/
    - xform/^(\w+?)an(\d)(;.*)$/$1Ⓕ$2$3/
    - xform/^(\w+?)(iang|uang)(\d)(;.*)$/$1Ⓝ$3$4/
    - xform/^(\w+?)iao(\d)(;.*)$/$1Ⓜ$2$3/
    - xform/^(\w+?)ang(\d)(;.*)$/$1Ⓖ$2$3/
    - xform/^(\w+?)eng(\d)(;.*)$/$1Ⓗ$2$3/
    - xform/^(\w+?)ing(\d)(;.*)$/$1Ⓙ$2$3/
    - xform/^(\w+?)ai(\d)(;.*)$/$1Ⓚ$2$3/
    - xform/^(\w+?)(in|er)(\d)(;.*)$/$1Ⓛ$3$4/
    - xform/^(\w+?)[vu]n(\d)(;.*)$/$1Ⓩ$2$3/
    - xform/^(\w+?)[vu]e(\d)(;.*)$/$1Ⓧ$2$3/
    - xform/^(\w+?)ao(\d)(;.*)$/$1Ⓒ$2$3/
    - xform/^(\w+?)(v|ui)(\d)(;.*)$/$1Ⓥ$3$4/
    - xform/^(\w+?)ei(\d)(;.*)$/$1Ⓑ$2$3/
    - xform/^zh/Ⓥ/
    - xform/^ch/Ⓘ/
    - xform/^sh/Ⓤ/
    - xlit/ⓆⓌⓇⓉⓎⓊⒾⓄⓅⓈⒹⒻⒼⒽⒿⓀⓁⓏⓍⒸⓋⒷⓃⓂ/qwrtyuiopsdfghjklzxcvbnm/


直接辅助:  ########################################位于词库第三个分号后
  __append:
#以下是双拼运算
    - derive/^(.*?)(\d?);.*$/$1/ # 纯双拼的情况
#分号后面第一组辅助码
    - derive/^(.*?)(\d?);.*$/$1$2/        # 双拼+声调的情况
    - derive/^(.*?)(\d?);(\w).*$/$1$3/        # 双拼+一位辅助码的情况
    - derive|^(.*?)(\d?);(\w).*$|$1$2$3|  # 双拼+声调+1位辅助码
    - derive|^(.*?)(\d?);(\w).*$|$1$3$2|  # 双拼+1位辅助码+声调
    - abbrev/^(.*?)(\d?);(\w)(\w).*$/$1$3$4/  # 双拼+2位辅助码的情况
    - derive|^(.*?)(\d?);(\w)(\w).*$|$1$3$4$2| # 双拼+字母 字母 数字
    - derive|^(.*?)(\d?);(\w)(\w).*$|$1$2$3$4|  # 双拼+数字 字母 字母
    - derive|^(.*?)(\d?);(\w)(\w).*$|$1$3$2$4|  # 双拼+字母 数字 字母
    - derive|^(.*?)(\d?);(\w)(\w).*$|$1$3$4/| # 双拼+2位辅助码+/
    - derive|^(.*?)(\d?);(\w)(\w).*$|$1$3$4o| # 双拼\+2位辅助码\+o，主要用来应对小企鹅这类输入法没法使用符号的场景，但同时有些辅助码会出现异常，如：ui uio>uiuio，手机小企鹅使用时开启
#分号后面第二组辅助码
    - derive/^(.*?)(\d?);.*?,(\w).*$/$1$3/        # 双拼+一位辅助码的情况
    - derive|^(.*?)(\d?);.*?,(\w).*$|$1$2$3|  # 双拼+声调+1位辅助码
    - derive|^(.*?)(\d?);.*?,(\w).*$|$1$3$2|  # 双拼+1位辅助码+声调
    - abbrev/^(.*?)(\d?);.*?,(\w)(\w).*$/$1$3$4/  # 双拼+2位辅助码的情况
    - derive|^(.*?)(\d?);.*?,(\w)(\w).*$|$1$3$4$2| # 双拼+字母 字母 数字
    - derive|^(.*?)(\d?);.*?,(\w)(\w).*$|$1$2$3$4|  # 双拼+数字 字母 字母
    - derive|^(.*?)(\d?);.*?,(\w)(\w).*$|$1$3$2$4|  # 双拼+字母 数字 字母
    - derive|^(.*?)(\d?);.*?,(\w)(\w).*$|$1$3$4/| # 双拼+2位辅助码+/
    - derive|^(.*?)(\d?);.*?,(\w)(\w).*$|$1$3$4o| # 双拼\+2位辅助码\+o，主要用来应对小企鹅这类输入法没法使用符号的场景，但同时有些辅助码会出现异常，如：ui uio>uiuio，手机小企鹅使用时开启
#分号后面第三组辅助码
    - derive/^(.*?)(\d?);.*?,.*?,(\w).*$/$1$3/        # 双拼+一位辅助码的情况
    - derive|^(.*?)(\d?);.*?,.*?,(\w).*$|$1$2$3|  # 双拼+声调+1位辅助码
    - derive|^(.*?)(\d?);.*?,.*?,(\w).*$|$1$3$2|  # 双拼+1位辅助码+声调
    - abbrev/^(.*?)(\d?);.*?,.*?,(\w)(\w).*$/$1$3$4/  # 双拼+2位辅助码的情况
    - derive|^(.*?)(\d?);.*?,.*?,(\w)(\w).*$|$1$3$4$2| # 双拼+字母 字母 数字
    - derive|^(.*?)(\d?);.*?,.*?,(\w)(\w).*$|$1$2$3$4|  # 双拼+数字 字母 字母
    - derive|^(.*?)(\d?);.*?,.*?,(\w)(\w).*$|$1$3$2$4|  # 双拼+字母 数字 字母
    - derive|^(.*?)(\d?);.*?,.*?,(\w)(\w).*$|$1$3$4/| # 双拼+2位辅助码+/
    - derive|^(.*?)(\d?);.*?,.*?,(\w)(\w).*$|$1$3$4o| # 双拼\+2位辅助码\+o，主要用来应对小企鹅这类输入法没法使用符号的场景，但同时有些辅助码会出现异常，如：ui uio>uiuio，手机小企鹅使用时开启
#分号后面第四组辅助码
    - derive/^(.*?)(\d?);.*?,.*?,.*?,(\w).*$/$1$3/        # 双拼+一位辅助码的情况
    - derive|^(.*?)(\d?);.*?,.*?,.*?,(\w).*$|$1$2$3|  # 双拼+声调+1位辅助码
    - derive|^(.*?)(\d?);.*?,.*?,.*?,(\w).*$|$1$3$2|  # 双拼+1位辅助码+声调
    - abbrev/^(.*?)(\d?);.*?,.*?,.*?,(\w)(\w).*$/$1$3$4/  # 双拼+2位辅助码的情况
    - derive|^(.*?)(\d?);.*?,.*?,.*?,(\w)(\w).*$|$1$3$4$2| # 双拼+字母 字母 数字
    - derive|^(.*?)(\d?);.*?,.*?,.*?,(\w)(\w).*$|$1$2$3$4|  # 双拼+数字 字母 字母
    - derive|^(.*?)(\d?);.*?,.*?,.*?,(\w)(\w).*$|$1$3$2$4|  # 双拼+字母 数字 字母
    - derive|^(.*?)(\d?);.*?,.*?,.*?,(\w)(\w).*$|$1$3$4/| # 双拼+2位辅助码+/
    - derive|^(.*?)(\d?);.*?,.*?,.*?,(\w)(\w).*$|$1$3$4o| # 双拼\+2位辅助码\+o，主要用来应对小企鹅这类输入法没法使用符号的场景，但同时有些辅助码会出现异常，如：ui uio>uiuio，手机小企鹅使用时开启
#特殊优化：部分音节字极少，几乎不参与构词，则提升对应四码字的优先级，仅对自然码双拼有效，其他双拼方案需作对应修改）
#    - derive/^(.*?)(\d?);(bd|dw|df|dw|yl|rw|fs|iw)$/$1$3/
#    - derive/^(.*?)(\d?);.*?,(bd|dw|df|dw|yl|rw|fs|iw)$/$1$3/
#    - derive/^(.*?)(\d?);.*?,.*?,(bd|dw|df|dw|yl|rw|fs|iw)$/$1$3/
#    - derive/^(.*?)(\d?);.*?,.*?,.*?,(bd|dw|df|dw|yl|rw|fs|iw)$/$1$3/
#    - erase/^(.+);(.+)$/    # 删除原始编码加速检索
    - xform/◯/;/  #对于微软双拼、搜狗双拼，前面将分号保护了起来，现在放出来让其发挥作用

间接辅助:  ########################################位于词库第三个分号后
  __append:
#以下是双拼运算
    - derive/^(.*?)(\d?);.*$/$1/ # 纯双拼的情况
    - derive/^(.*?)(\d?);.*$/$1$2/ # 双拼+直接声调的情况
#分号后面第一组辅助码
    - derive|^(.*?)(\d?);(\w).*$|$1/$3| # 双拼+一位辅助码的情况
    - derive|^(.*?)(\d?);(\w).*$|$1$2/$3| # 双拼+直接声调+/间接一位辅助码的情况
    - derive|^(.*?)(\d?);(\w)(\w).*$|$1/$3$4| # 双拼+2位辅助码的情况
    - derive|^(.*?)(\d?);(\w)(\w).*$|$1$2/$3$4| # 双拼+直接声调+/间接2位辅助码的情况
#分号后面第二组辅助码
    - derive|^(.*?)(\d?);.*?,(\w).*$|$1/$3| # 双拼+一位辅助码的情况
    - derive|^(.*?)(\d?);.*?,(\w).*$|$1$2/$3| # 双拼+直接声调+/间接一位辅助码的情况
    - derive|^(.*?)(\d?);.*?,(\w)(\w).*$|$1/$3$4| # 双拼+2位辅助码的情况
    - derive|^(.*?)(\d?);.*?,(\w)(\w).*$|$1$2/$3$4| # 双拼+直接声调+/间接2位辅助码的情况
#分号后面第三组辅助码
    - derive|^(.*?)(\d?);.*?,.*?,(\w).*$|$1/$3| # 双拼+一位辅助码的情况
    - derive|^(.*?)(\d?);.*?,.*?,(\w).*$|$1$2/$3| # 双拼+直接声调+/间接一位辅助码的情况
    - derive|^(.*?)(\d?);.*?,.*?,(\w)(\w).*$|$1/$3$4| # 双拼+2位辅助码的情况
    - derive|^(.*?)(\d?);.*?,.*?,(\w)(\w).*$|$1$2/$3$4| # 双拼+直接声调+/间接2位辅助码的情况
#分号后面第四组辅助码
    - derive|^(.*?)(\d?);.*?,.*?,.*?,(\w).*$|$1/$3| # 双拼+一位辅助码的情况
    - derive|^(.*?)(\d?);.*?,.*?,.*?,(\w).*$|$1$2/$3| # 双拼+直接声调+/间接一位辅助码的情况
    - derive|^(.*?)(\d?);.*?,.*?,.*?,(\w)(\w).*$|$1/$3$4| # 双拼+2位辅助码的情况
    - derive|^(.*?)(\d?);.*?,.*?,.*?,(\w)(\w).*$|$1$2/$3$4| # 双拼+直接声调+/间接2位辅助码的情况
    - erase/^(.+);(.+)$/    # 删除原始编码加速检索
    - xform/◯/;/  #对于微软双拼、搜狗双拼，前面将分号保护了起来，现在放出来让其发挥作用
mohuyin:
  __append:
    # 模糊音 可选择性开启
    - derive/^z([a-z])/v$1/
    - derive/^c([a-z])/i$1/
    - derive/^s([a-z])/u$1/
    - derive/^v([a-z])/z$1/
    - derive/^i([a-z])/c$1/
    - derive/^u([a-z])/s$1/

user_dict_set:
  dictionary: wanxiang_pro
  initial_quality: 0
  enable_completion: false
  enable_sentence: false
  spelling_hints: 100
  comment_format:
  preedit_format:     #影响到输入框的显示和“Shift+回车”上屏的字符，交给super_preedit处理
    - xform/7/¹/
    - xform/8/²/
    - xform/9/³/
    - xform/0/⁴/
  enable_user_dict: true
  user_dict: zc


  # 自定义词典加词(ac引导)
add_user_dict:
  tag: add_user_dict
  dictionary: wanxiang_pro
  initial_quality: -1
  user_dict: zc
  enable_charset_filter: false # 是否开启字符集过滤
  enable_completion: true # 提前显示尚未输入完整码的字〔仅 table_translator 有效〕
  enable_encoder: true # 是否开启自动造词〔仅 table_translator 有效〕
  enable_sentence: true #  是否开启自动造句
  enable_user_dict: true
  encode_commit_history: false # 是否对已上屛词自动成词〔仅 table_translator 有效〕  
  comment_format:
  prefix: "``"
  tips: "〔开始造词〕"

#语法模型
octagram:
  __patch:
    grammar:
      language: wanxiang-lts-zh-hans
      collocation_max_length: 8         #命中的最长词组
      collocation_min_length: 2         #命中的最短词组，搭配词频健全的词库时候应当最小值设为3避开2字高频词
      collocation_penalty: -10          #默认-12 对常见搭配词组施加的惩罚值。较高的负值会降低这些搭配被选中的概率，防止过于频繁地出现某些固定搭配。
      non_collocation_penalty: -12      #默认-12 对非搭配词组施加的惩罚值。较高的负值会降低非搭配词组被选中的概率，避免不合逻辑或不常见的词组组合。
      weak_collocation_penalty: -24     #默认-24 对弱搭配词组施加的惩罚值。保持默认值通常是为了有效过滤掉不太常见但仍然合理的词组组合。
      rear_penalty: -18                 #默认-18 对词组中后续词语的位置施加的惩罚值。较高的负值会降低某些词语在句子后部出现的概率，防止句子结构不自然。
    translator/contextual_suggestions: false
    translator/max_homophones: 5
    translator/max_homographs: 5

#并击处理器
set_chord_composer:
  __patch:
    chord_composer:
      finish_chord_on_first_key_release: true #有一键被释放时立刻触发合成
      alphabet: qazwsxedcrfvtgbyhnujmik,ol.p;/ `
      algebra:
      # 符号和数字映射为大写字母，分离其转义干扰
        - xlit|;,./|ACXZ|
      # 左手编码包装
        - xform/([qwertasdfgzxcvb]+)/<$1>/
      # 右手编码包装
        - xform/([yuiophjklAnmCXZ]+)/<$1>/
      # 主处理区（尽量按照顺序排列）
        - xform=(<q>|<p>)=q=
        - xform=(<w>|<o>)=w=
        - xform=(<e>|<i>)=e=
        - xform=(<r>|<u>)=r=
        - xform=(<t>|<y>)=t=
        - xform=(<ef>|<ji>)=y=
        - xform=(<er>|<ui>)=u=
        - xform=(<we>|<io>)=i=
        - xform=(<wr>|<uo>)=o=
        - xform=(<qr>|<up>)=p=
        - xform=(<a>|<A>)=a=
        - xform=(<s>|<l>)=s=
        - xform=(<d>|<k>)=d=
        - xform=(<f>|<j>)=f=
        - xform=(<g>|<h>)=g=
        - xform=(<se>|<il>)=h=
        - xform=(<wf>|<jo>)=h=
        - xform=(<df>|<jk>)=j=
        - xform=(<sd>|<kl>)=k=
        - xform=(<sf>|<jl>)=l=
        - xform=(<z>|<Z>)=z=
        - xform=(<x>|<X>)=x=
        - xform=(<c>|<C>)=c=
        - xform=(<v>|<m>)=v=
        - xform=(<b>|<n>)=b=
        - xform=(<af>|<jA>)=n=
        - xform=(<cv>|<mC>)=m=
        - xform=(<ad>|<kA>)=;=
        - xform=(<zx>|<XZ>)=/=
        - xform=(<xc>|<CX>)=,=
        - xform=(<xv>|<mX>)=.=
        - xform=(<zc>|<CZ>)=!=
        - xform=(<zb>|<nZ>)=!=
        - xform=(<zv>|<mZ>)=?=
        - xform=(<ar>|<uA>)=:“=
        - xform=(<sr>|<ul>)=.”=
        - xform=(<ed>|<ik>)=!”=
        - xform=(<rf>|<uj>)=?”=
        - xform=(<aw>|<oA>)=^”=
        - xform=(<zf>|<jZ>)=~”=
        - xform=(<xf>|<jX>)=,“=
        - xform=(<dg>|<hk>)=+=
        - xform=(<ag>|<hA>)=-=
        - xform=(<et>|<yi>)=%=
        - xform=(<as>|<lA>)=*= 
        - xform=(<qe>|<ip>)=@=
        - xform=(<tg>)=:=
        - xform=(<yh>)=#=
        - xform=(<fg>)=~=
        - xform=(<hj>)=^=
        - xform=(<rt>)=“=
        - xform=(<yu>)=”==
      #  - xform=(<qt>|<yp>)==
      #  - xform=(<rt>|<yu>)==
      #  - xform=(<fg>|<hj>)==
      #  - xform=(<tg>|<yh>)==   
      #  - xform=(<qw>|<op>)==
  ###################################
        # 屏蔽未定义的指法
        - xform/<1>/ /
        - xform/<"1>/"/
        - xform/^.*<.+>.*$//
        # 符号转义的后处理
        - xform=，="""",=
        - xform=。="""".=
        - xform=！=""""!=
        - xform=？=""""?=
