patch:
  speller/algebra:
    __patch:
      - wanxiang_pro.schema:/小鹤双拼            # 可选输入方案名称：自然码, 自然龙, 小鹤双拼, 搜狗双拼, 微软双拼, 智能ABC, 紫光双拼, 国标双拼
      #- wanxiang_pro.schema:/fuzhu_moqi        # release下载的不需要这个，直接下载仓库才需要(用来选七种辅助码)，用来定义不同的辅助码，有需求打开pro主方案自己看吧
      - wanxiang_pro.schema:/直接辅助            #辅助码升级为：直接辅助和间接辅助两种类型，都是句中任意，不同点在于直接辅助是nire=你  而间接则需要/引导  ni/re=你 ，在这个基础上直接辅助支持拼音后任意位置数字声调参与，间接辅助声调在/引导前参与
##########################以上格式受指令初始化控制，最好保持格式不变，如果发生变更请不要使用指令修改相关数据#####################################
  #通过下面的设置可以让你自己的文件引入而与仓库custom_phrase.txt不同，防止更新时被仓库文件覆盖
  #custom_phrase/user_dict: custom_phrasexx    # 这里改成什么就需要手动创建同名的 custom_phrasexx.txt 文件在用户目录，这个文件主要用于置顶，编码为自定义编码的词汇
  #translator/packs/+:
    #- userxx                                  #导入根目录下名称为userxx.dict.yaml的自定义固定词典，编码要与固定词库一致，编码权重都不要少，形如姓名、专有名词公司名称等等
    #下面是候选数量，未来7890分别代表1234声，请候选长度不要大于6避免冲突
  menu/page_size: 5
  #生日信息：/sr或者osr，在这里定义全局替换构建你的生日查询数据库
  birthday_reminder:  #日期格式：必须是4位数字，格式为MMDD（月份和日期），例如：1月27日 → 0127 ，#备注格式：在日期后添加逗号，然后添加任意文本作为备注，例如："0501,我的好朋友"，也可以无备注
    solar_birthdays:  # 公历生日, 姓名: "日期,备注" or 姓名: "日期"
      小明: "0501,准备礼物"
      大明: "0405"
    lunar_birthdays:  # 农历生日, 姓名: "日期,备注" or 姓名: "日期"
      小明: "0114"
      小红: "0815,农历中秋"
  #下面用来改变你的windows小狼毫右下角软件图标
  #schema/+:
  #  icon: "icons/zhong.ico"
  #  ascii_icon: "icons/ying.ico"
  #下面这个可以改变tips上屏的按键
  key_binder/tips_key: "period"   #修改时候去default找，默认是句号
  key_binder/sequence: # Lua 配置：手动排序的快捷键 super_sequence.lua，不要用方向键，各种冲突，一定要避免冲突
    up: "Control+j"    # 上移
    down: "Control+k"  # 下移
    reset: "Control+l" # 重置
    pin: "Control+p"   # 置顶
  #下面这个是修改快符的映射，按自己需求来
  quick_symbol_text:
    q: "‰"
    w: "？"
    e: "（"
    r: "）"
    t: "~"
    y: "·"
    u: "『"
    i: "』"
    o: "〖"
    p: "〗"
    a: "！"
    s: "……"
    d: "、"
    f: "“"
    g: "”"
    h: "‘"
    j: "’"
    k: "【"
    l: "】"
    z: "。”"
    x: "？”"
    c: "！”"
    v: "——"
    b: "%"
    n: "《"
    m: "》"
    "1": "①"
    "2": "②"
    "3": "③"
    "4": "④"
    "5": "⑤"
    "6": "⑥"
    "7": "⑦"
    "8": "⑧"
    "9": "⑨"
    "0": "⓪"
  #下面这两个是快符的引导符号，前者用来引导符号、双击重复上屏符号，后者双击重复上屏汉字
  recognizer/patterns/quick_symbol: "^;.*$"
  #下面这个用来设置开启调频的时候哪些内容不调频
  translator/disable_user_dict_for_patterns: "^[a-z]{1,6}"

  # 以词定字,使用 @before.0 添加到processors列表开头
  engine/processors/@before.0: lua_processor@*select_character
  
  # 分号键用于次选,使用 @before.0 添加到bindings的开头
  key_binder/bindings/@before.0: { when: has_menu, accept: semicolon, send: 2 }

  # 大写辅助码加到整句倒数第二个音节,使用 /+ 添加到bindings的末尾
  key_binder/bindings/+:
    - { when: has_menu, accept: Shift+A, send_sequence: "{Shift+Left}a{Shift+Right}" }
    - { when: has_menu, accept: Shift+B, send_sequence: "{Shift+Left}b{Shift+Right}" }
    - { when: has_menu, accept: Shift+C, send_sequence: "{Shift+Left}c{Shift+Right}" }
    - { when: has_menu, accept: Shift+D, send_sequence: "{Shift+Left}d{Shift+Right}" }
    - { when: has_menu, accept: Shift+E, send_sequence: "{Shift+Left}e{Shift+Right}" }
    - { when: has_menu, accept: Shift+F, send_sequence: "{Shift+Left}f{Shift+Right}" }
    - { when: has_menu, accept: Shift+G, send_sequence: "{Shift+Left}g{Shift+Right}" }
    - { when: has_menu, accept: Shift+H, send_sequence: "{Shift+Left}h{Shift+Right}" }
    - { when: has_menu, accept: Shift+I, send_sequence: "{Shift+Left}i{Shift+Right}" }
    - { when: has_menu, accept: Shift+J, send_sequence: "{Shift+Left}j{Shift+Right}" }
    - { when: has_menu, accept: Shift+K, send_sequence: "{Shift+Left}k{Shift+Right}" }
    - { when: has_menu, accept: Shift+L, send_sequence: "{Shift+Left}l{Shift+Right}" }
    - { when: has_menu, accept: Shift+M, send_sequence: "{Shift+Left}m{Shift+Right}" }
    - { when: has_menu, accept: Shift+N, send_sequence: "{Shift+Left}n{Shift+Right}" }
    - { when: has_menu, accept: Shift+O, send_sequence: "{Shift+Left}o{Shift+Right}" }
    - { when: has_menu, accept: Shift+P, send_sequence: "{Shift+Left}p{Shift+Right}" }
    - { when: has_menu, accept: Shift+Q, send_sequence: "{Shift+Left}q{Shift+Right}" }
    - { when: has_menu, accept: Shift+R, send_sequence: "{Shift+Left}r{Shift+Right}" }
    - { when: has_menu, accept: Shift+S, send_sequence: "{Shift+Left}s{Shift+Right}" }
    - { when: has_menu, accept: Shift+T, send_sequence: "{Shift+Left}t{Shift+Right}" }
    - { when: has_menu, accept: Shift+U, send_sequence: "{Shift+Left}u{Shift+Right}" }
    - { when: has_menu, accept: Shift+V, send_sequence: "{Shift+Left}v{Shift+Right}" }
    - { when: has_menu, accept: Shift+W, send_sequence: "{Shift+Left}w{Shift+Right}" }
    - { when: has_menu, accept: Shift+X, send_sequence: "{Shift+Left}x{Shift+Right}" }
    - { when: has_menu, accept: Shift+Y, send_sequence: "{Shift+Left}y{Shift+Right}" }
    - { when: has_menu, accept: Shift+Z, send_sequence: "{Shift+Left}z{Shift+Right}" }
