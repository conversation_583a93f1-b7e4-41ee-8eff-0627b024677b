__build_info:
  rime_version: 1.13.1
  timestamps:
    default: 1754711178
    default.custom: 1753237411
ascii_composer:
  good_old_caps_lock: true
  switch_key:
    Caps_Lock: clear
    Control_L: noop
    Control_R: noop
    Shift_L: commit_code
    Shift_R: commit_code
config_version: LTS
key_binder:
  bindings:
    - {accept: "Shift+Tab", send: "Shift+Left", when: composing}
    - {accept: "Alt+Left", send: "Shift+Left", when: composing}
    - {accept: "Alt+Right", send: "Shift+Right", when: composing}
    - {accept: minus, send: Page_Up, when: has_menu}
    - {accept: equal, send: Page_Down, when: has_menu}
    - {accept: "Control+。", toggle: ascii_punct, when: always}
    - {accept: "Control+period", toggle: ascii_punct, when: always}
    - {accept: "Control+Shift+F", toggle: s2s, when: always}
    - {accept: "Control+Shift+F", toggle: s2s, when: always}
    - {accept: "Control+Shift+5", toggle: full_shape, when: always}
    - {accept: "Control+Shift+percent", toggle: full_shape, when: always}
    - {accept: "Control+k", send: "Shift+Delete", when: composing}
    - {accept: KP_0, send: 0, when: composing}
    - {accept: KP_1, send: 1, when: composing}
    - {accept: KP_2, send: 2, when: composing}
    - {accept: KP_3, send: 3, when: composing}
    - {accept: KP_4, send: 4, when: composing}
    - {accept: KP_5, send: 5, when: composing}
    - {accept: KP_6, send: 6, when: composing}
    - {accept: KP_7, send: 7, when: composing}
    - {accept: KP_8, send: 8, when: composing}
    - {accept: KP_9, send: 9, when: composing}
    - {accept: KP_Decimal, send: period, when: composing}
    - {accept: KP_Multiply, send: asterisk, when: composing}
    - {accept: KP_Add, send: plus, when: composing}
    - {accept: KP_Subtract, send: minus, when: composing}
    - {accept: KP_Divide, send: slash, when: composing}
    - {accept: KP_Enter, send: Return, when: composing}
  select_first_character: bracketleft
  select_last_character: bracketright
menu:
  alternative_select_labels:
    - 1
    - 2
    - 3
    - 4
    - 5
    - 6
    - 7
    - 8
    - 9
    - 10
  page_size: 6
recognizer:
  patterns:
    email: "^[A-Za-z][-_.0-9A-Za-z]*@.*$"
    underscore: "^[A-Za-z]+_.*"
    url: "^(www[.]|https?:|ftp[.:]|mailto:|file:).*$|^[a-z]+[.].+$"
schema_list:
  - schema: wanxiang_pro
switcher:
  abbreviate_options: true
  caption: "「方案选单」"
  fold_options: true
  hotkeys:
    - "Control+grave"
  option_list_separator: " / "
  save_options:
    - ascii_punct
    - s2t
    - s2hk
    - s2tw
    - emoji
    - full_shape
    - prediction
    - super_tips
    - charset_filter
    - chaifen_switch
    - tone_display
    - fuzhu_hint
    - tone_hint
    - chinese_english
    - search_single_char