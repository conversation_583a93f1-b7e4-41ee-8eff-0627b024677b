__build_info:
  rime_version: 1.13.1
  timestamps:
    default: 1754711178
    default.custom: 1753237411
    wanxiang_reverse.custom: 1754711578
    wanxiang_reverse.schema: 1754711177
engine:
  filters:
    - uniquifier
  processors:
    - key_binder
    - speller
    - selector
    - navigator
    - express_editor
  segmentors:
    - abc_segmentor
  translators:
    - echo_translator
    - table_translator
key_binder:
  bindings:
    - {accept: "Shift+Tab", send: "Shift+Left", when: composing}
    - {accept: "Alt+Left", send: "Shift+Left", when: composing}
    - {accept: "Alt+Right", send: "Shift+Right", when: composing}
    - {accept: minus, send: Page_Up, when: has_menu}
    - {accept: equal, send: Page_Down, when: has_menu}
    - {accept: "Control+。", toggle: ascii_punct, when: always}
    - {accept: "Control+period", toggle: ascii_punct, when: always}
    - {accept: "Control+Shift+F", toggle: s2s, when: always}
    - {accept: "Control+Shift+F", toggle: s2s, when: always}
    - {accept: "Control+Shift+5", toggle: full_shape, when: always}
    - {accept: "Control+Shift+percent", toggle: full_shape, when: always}
    - {accept: "Control+k", send: "Shift+Delete", when: composing}
    - {accept: KP_0, send: 0, when: composing}
    - {accept: KP_1, send: 1, when: composing}
    - {accept: KP_2, send: 2, when: composing}
    - {accept: KP_3, send: 3, when: composing}
    - {accept: KP_4, send: 4, when: composing}
    - {accept: KP_5, send: 5, when: composing}
    - {accept: KP_6, send: 6, when: composing}
    - {accept: KP_7, send: 7, when: composing}
    - {accept: KP_8, send: 8, when: composing}
    - {accept: KP_9, send: 9, when: composing}
    - {accept: KP_Decimal, send: period, when: composing}
    - {accept: KP_Multiply, send: asterisk, when: composing}
    - {accept: KP_Add, send: plus, when: composing}
    - {accept: KP_Subtract, send: minus, when: composing}
    - {accept: KP_Divide, send: slash, when: composing}
    - {accept: KP_Enter, send: Return, when: composing}
  select_first_character: bracketleft
  select_last_character: bracketright
menu:
  alternative_select_labels:
    - 1
    - 2
    - 3
    - 4
    - 5
    - 6
    - 7
    - 8
    - 9
    - 10
  page_size: 6
schema:
  author: amzxyz
  description: |
    万象的反查功能模块，方案融合了组字与笔画的能力

  name: "万象：部件与笔画反查"
  schema_id: wanxiang_reverse
  version: LTS
set_shuru_schema:
  - "xform/'//"
  - "derive/^([nl])ue$/$1ve/"
  - "derive/'([nl])ue$/'$1ve/"
  - "derive/^([jqxy])u/$1v/"
  - "derive/'([jqxy])u/'$1v/"
speller:
  algebra:
    - "derive/^([jqxy])u(?=^|$|')/$1v/"
    - "derive/'([jqxy])u(?=^|$|')/'$1v/"
    - "derive/^([aoe])([ioun])(?=^|$|')/$1$1$2/"
    - "derive/'([aoe])([ioun])(?=^|$|')/'$1$1$2/"
    - "xform/^([aoe])(ng)?(?=^|$|')/$1$1$2/"
    - "xform/'([aoe])(ng)?(?=^|$|')/'$1$1$2/"
    - "xform/iu(?=^|$|')/<q>/"
    - "xform/(.)ei(?=^|$|')/$1<w>/"
    - "xform/uan(?=^|$|')/<r>/"
    - "xform/[uv]e(?=^|$|')/<t>/"
    - "xform/un(?=^|$|')/<y>/"
    - "xform/^sh/<u>/"
    - "xform/^ch/<i>/"
    - "xform/^zh/<v>/"
    - "xform/'sh/'<u>/"
    - "xform/'ch/'<i>/"
    - "xform/'zh/'<v>/"
    - "xform/uo(?=^|$|')/<o>/"
    - "xform/ie(?=^|$|')/<p>/"
    - "xform/([a-z>])i?ong(?=^|$|')/$1<s>/"
    - "xform/ing(?=^|$|')|uai(?=^|$|')/<k>/"
    - "xform/([a-z>])ai(?=^|$|')/$1<d>/"
    - "xform/([a-z>])en(?=^|$|')/$1<f>/"
    - "xform/([a-z>])eng(?=^|$|')/$1<g>/"
    - "xform/[iu]ang(?=^|$|')/<l>/"
    - "xform/([a-z>])ang(?=^|$|')/$1<h>/"
    - "xform/ian(?=^|$|')/<m>/"
    - "xform/([a-z>])an(?=^|$|')/$1<j>/"
    - "xform/([a-z>])ou(?=^|$|')/$1<z>/"
    - "xform/[iu]a(?=^|$|')/<x>/"
    - "xform/iao(?=^|$|')/<n>/"
    - "xform/([a-z>])ao(?=^|$|')/$1<c>/"
    - "xform/ui(?=^|$|')/<v>/"
    - "xform/in(?=^|$|')/<b>/"
    - "xform/'|<|>//"
  alphabet: "abcdefghijklmnopqrstuvwxyz;"
  delimiter: " '"
translator:
  dictionary: wanxiang_reverse
  enable_user_dict: false
"全拼":
  - "xform/'//"
  - "derive/^([nl])ue$/$1ve/"
  - "derive/'([nl])ue$/'$1ve/"
  - "derive/^([jqxy])u/$1v/"
  - "derive/'([jqxy])u/'$1v/"
"国标双拼":
  - "derive/^([aoe])([ioun])(?=^|$|')/$1$1$2/"
  - "derive/'([aoe])([ioun])(?=^|$|')/'$1$1$2/"
  - "xform/^([aoe])(ng)?(?=^|$|')/$1$1$2/"
  - "xform/'([aoe])(ng)?(?=^|$|')/'$1$1$2/"
  - "xform/iu(?=^|$|')/<y>/"
  - "xform/(.)ei(?=^|$|')/$1<b>/"
  - "xform/uan(?=^|$|')/<w>/"
  - "xform/[uv]e(?=^|$|')/<x>/"
  - "xform/un(?=^|$|')/<z>/"
  - "xform/^sh/<u>/"
  - "xform/^ch/<i>/"
  - "xform/^zh/<v>/"
  - "xform/'sh/'<u>/"
  - "xform/'ch/'<i>/"
  - "xform/'zh/'<v>/"
  - "xform/uo(?=^|$|')/<o>/"
  - "xform/ie(?=^|$|')/<t>/"
  - "xform/([a-z>])i?ong(?=^|$|')/$1<s>/"
  - "xform/ing(?=^|$|')|uai(?=^|$|')/<j>/"
  - "xform/([a-z>])ai(?=^|$|')/$1<k>/"
  - "xform/([a-z>])en(?=^|$|')/$1<r>/"
  - "xform/([a-z>])eng(?=^|$|')/$1<h>/"
  - "xform/[iu]ang(?=^|$|')/<n>/"
  - "xform/([a-z>])ang(?=^|$|')/$1<g>/"
  - "xform/ian(?=^|$|')/<d>/"
  - "xform/([a-z>])an(?=^|$|')/$1<f>/"
  - "xform/([a-z>])ou(?=^|$|')/$1<p>/"
  - "xform/[iu]a(?=^|$|')/<q>/"
  - "xform/iao(?=^|$|')/<m>/"
  - "xform/([a-z>])ao(?=^|$|')/$1<c>/"
  - "xform/ui(?=^|$|')/<v>/"
  - "xform/in(?=^|$|')/<l>/"
  - "xform/'|<|>//"
"小鹤双拼":
  - "derive/^([jqxy])u(?=^|$|')/$1v/"
  - "derive/'([jqxy])u(?=^|$|')/'$1v/"
  - "derive/^([aoe])([ioun])(?=^|$|')/$1$1$2/"
  - "derive/'([aoe])([ioun])(?=^|$|')/'$1$1$2/"
  - "xform/^([aoe])(ng)?(?=^|$|')/$1$1$2/"
  - "xform/'([aoe])(ng)?(?=^|$|')/'$1$1$2/"
  - "xform/iu(?=^|$|')/<q>/"
  - "xform/(.)ei(?=^|$|')/$1<w>/"
  - "xform/uan(?=^|$|')/<r>/"
  - "xform/[uv]e(?=^|$|')/<t>/"
  - "xform/un(?=^|$|')/<y>/"
  - "xform/^sh/<u>/"
  - "xform/^ch/<i>/"
  - "xform/^zh/<v>/"
  - "xform/'sh/'<u>/"
  - "xform/'ch/'<i>/"
  - "xform/'zh/'<v>/"
  - "xform/uo(?=^|$|')/<o>/"
  - "xform/ie(?=^|$|')/<p>/"
  - "xform/([a-z>])i?ong(?=^|$|')/$1<s>/"
  - "xform/ing(?=^|$|')|uai(?=^|$|')/<k>/"
  - "xform/([a-z>])ai(?=^|$|')/$1<d>/"
  - "xform/([a-z>])en(?=^|$|')/$1<f>/"
  - "xform/([a-z>])eng(?=^|$|')/$1<g>/"
  - "xform/[iu]ang(?=^|$|')/<l>/"
  - "xform/([a-z>])ang(?=^|$|')/$1<h>/"
  - "xform/ian(?=^|$|')/<m>/"
  - "xform/([a-z>])an(?=^|$|')/$1<j>/"
  - "xform/([a-z>])ou(?=^|$|')/$1<z>/"
  - "xform/[iu]a(?=^|$|')/<x>/"
  - "xform/iao(?=^|$|')/<n>/"
  - "xform/([a-z>])ao(?=^|$|')/$1<c>/"
  - "xform/ui(?=^|$|')/<v>/"
  - "xform/in(?=^|$|')/<b>/"
  - "xform/'|<|>//"
"微软双拼":
  - "derive/^([jqxy])u(?=^|$|')/$1v/"
  - "derive/'([jqxy])u(?=^|$|')/'$1v/"
  - "derive/^([aoe].*)(?=^|$|')/o$1/"
  - "derive/'([aoe].*)(?=^|$|')/'o$1/"
  - "xform/^([ae])(.*)(?=^|$|')/$1$1$2/"
  - "xform/'([ae])(.*)(?=^|$|')/'$1$1$2/"
  - "xform/iu(?=^|$|')/<q>/"
  - "xform/[iu]a(?=^|$|')/<w>/"
  - "xform/er(?=^|$|')|[uv]an(?=^|$|')/<r>/"
  - "xform/[uv]e(?=^|$|')/<t>/"
  - "xform/v(?=^|$|')|uai(?=^|$|')/<y>/"
  - "xform/^sh/<u>/"
  - "xform/^ch/<i>/"
  - "xform/^zh/<v>/"
  - "xform/'sh/'<u>/"
  - "xform/'ch/'<i>/"
  - "xform/'zh/'<v>/"
  - "xform/uo(?=^|$|')/<o>/"
  - "xform/[uv]n(?=^|$|')/<p>/"
  - "xform/([a-z>])i?ong(?=^|$|')/$1<s>/"
  - "xform/[iu]ang(?=^|$|')/<d>/"
  - "xform/([a-z>])en(?=^|$|')/$1<f>/"
  - "xform/([a-z>])eng(?=^|$|')/$1<g>/"
  - "xform/([a-z>])ang(?=^|$|')/$1<h>/"
  - "xform/ian(?=^|$|')/<m>/"
  - "xform/([a-z>])an(?=^|$|')/$1<j>/"
  - "xform/iao(?=^|$|')/<c>/"
  - "xform/([a-z>])ao(?=^|$|')/$1<k>/"
  - "xform/([a-z>])ai(?=^|$|')/$1<l>/"
  - "xform/([a-z>])ei(?=^|$|')/$1<z>/"
  - "xform/ie(?=^|$|')/<x>/"
  - "xform/ui(?=^|$|')/<v>/"
  - "derive/<t>(?=^|$|')/<v>/"
  - "xform/([a-z>])ou(?=^|$|')/$1<b>/"
  - "xform/in(?=^|$|')/<n>/"
  - "xform/ing(?=^|$|')/;/"
  - "xform/'|<|>//"
"拼音加加":
  - "derive/^([jqxy])u(?=^|$|')/$1v/"
  - "derive/'([jqxy])u(?=^|$|')/'$1v/"
  - "derive/^([aoe])([ioun])(?=^|$|')/$1$1$2/"
  - "derive/'([aoe])([ioun])(?=^|$|')/'$1$1$2/"
  - "xform/^([aoe])(ng)?(?=^|$|')/$1$1$2/"
  - "xform/'([aoe])(ng)?(?=^|$|')/'$1$1$2/"
  - "xform/iu(?=^|$|')/<n>/"
  - "xform/[iu]a(?=^|$|')/<b>/"
  - "xform/[uv]an(?=^|$|')/<c>/"
  - "xform/[uv]e(?=^|$|')|uai(?=^|$|')/<x>/"
  - "xform/ing(?=^|$|')|er(?=^|$|')/<q>/"
  - "xform/^sh/<i>/"
  - "xform/^ch/<u>/"
  - "xform/^zh/<v>/"
  - "xform/'sh/'<i>/"
  - "xform/'ch/'<u>/"
  - "xform/'zh/'<v>/"
  - "xform/uo(?=^|$|')/<o>/"
  - "xform/[uv]n(?=^|$|')/<z>/"
  - "xform/([a-z>])i?ong(?=^|$|')/$1<y>/"
  - "xform/[iu]ang(?=^|$|')/<h>/"
  - "xform/([a-z>])en(?=^|$|')/$1<r>/"
  - "xform/([a-z>])eng(?=^|$|')/$1<t>/"
  - "xform/([a-z>])ang(?=^|$|')/$1<g>/"
  - "xform/ian(?=^|$|')/<j>/"
  - "xform/([a-z>])an(?=^|$|')/$1<f>/"
  - "xform/iao(?=^|$|')/<k>/"
  - "xform/([a-z>])ao(?=^|$|')/$1<d>/"
  - "xform/([a-z>])ai(?=^|$|')/$1<s>/"
  - "xform/([a-z>])ei(?=^|$|')/$1<w>/"
  - "xform/ie(?=^|$|')/<m>/"
  - "xform/ui(?=^|$|')/<v>/"
  - "xform/([a-z>])ou(?=^|$|')/$1<p>/"
  - "xform/in(?=^|$|')/<l>/"
  - "xform/'|<|>//"
"搜狗双拼":
  - "derive/^([jqxy])u(?=^|$|')/$1v/"
  - "derive/'([jqxy])u(?=^|$|')/'$1v/"
  - "derive/^([aoe].*)(?=^|$|')/o$1/"
  - "derive/'([aoe].*)(?=^|$|')/'o$1/"
  - "xform/^([ae])(.*)(?=^|$|')/$1$1$2/"
  - "xform/'([ae])(.*)(?=^|$|')/'$1$1$2/"
  - "xform/iu(?=^|$|')/<q>/"
  - "xform/[iu]a(?=^|$|')/<w>/"
  - "xform/er(?=^|$|')|[uv]an(?=^|$|')/<r>/"
  - "xform/[uv]e(?=^|$|')/<t>/"
  - "xform/v(?=^|$|')|uai(?=^|$|')/<y>/"
  - "xform/^sh/<u>/"
  - "xform/^ch/<i>/"
  - "xform/^zh/<v>/"
  - "xform/'sh/'<u>/"
  - "xform/'ch/'<i>/"
  - "xform/'zh/'<v>/"
  - "xform/uo(?=^|$|')/<o>/"
  - "xform/[uv]n(?=^|$|')/<p>/"
  - "xform/([a-z>])i?ong(?=^|$|')/$1<s>/"
  - "xform/[iu]ang(?=^|$|')/<d>/"
  - "xform/([a-z>])en(?=^|$|')/$1<f>/"
  - "xform/([a-z>])eng(?=^|$|')/$1<g>/"
  - "xform/([a-z>])ang(?=^|$|')/$1<h>/"
  - "xform/ian(?=^|$|')/<m>/"
  - "xform/([a-z>])an(?=^|$|')/$1<j>/"
  - "xform/iao(?=^|$|')/<c>/"
  - "xform/([a-z>])ao(?=^|$|')/$1<k>/"
  - "xform/([a-z>])ai(?=^|$|')/$1<l>/"
  - "xform/([a-z>])ei(?=^|$|')/$1<z>/"
  - "xform/ie(?=^|$|')/<x>/"
  - "xform/ui(?=^|$|')/<v>/"
  - "xform/([a-z>])ou(?=^|$|')/$1<b>/"
  - "xform/in(?=^|$|')/<n>/"
  - "xform/ing(?=^|$|')/;/"
  - "xform/'|<|>//"
"智能ABC":
  - "xform/^zh/<a>/"
  - "xform/^ch/<e>/"
  - "xform/^sh/<v>/"
  - "xform/'zh/'<a>/"
  - "xform/'ch/'<e>/"
  - "xform/'sh/'<v>/"
  - "xform/^([aoe].*)(?=^|$|')/<o>$1/"
  - "xform/'([aoe].*)(?=^|$|')/'<o>$1/"
  - "xform/ei(?=^|$|')/<q>/"
  - "xform/ian(?=^|$|')/<w>/"
  - "xform/er(?=^|$|')|iu(?=^|$|')/<r>/"
  - "xform/[iu]ang(?=^|$|')/<t>/"
  - "xform/ing(?=^|$|')/<y>/"
  - "xform/uo(?=^|$|')/<o>/"
  - "xform/uan(?=^|$|')/<p>/"
  - "xform/([a-z>])i?ong(?=^|$|')/$1<s>/"
  - "xform/[iu]a(?=^|$|')/<d>/"
  - "xform/en(?=^|$|')/<f>/"
  - "xform/eng(?=^|$|')/<g>/"
  - "xform/ang(?=^|$|')/<h>/"
  - "xform/an(?=^|$|')/<j>/"
  - "xform/iao(?=^|$|')/<z>/"
  - "xform/ao(?=^|$|')/<k>/"
  - "xform/in(?=^|$|')|uai(?=^|$|')/<c>/"
  - "xform/ai(?=^|$|')/<l>/"
  - "xform/ie(?=^|$|')/<x>/"
  - "xform/ou(?=^|$|')/<b>/"
  - "xform/un(?=^|$|')/<n>/"
  - "xform/[uv]e(?=^|$|')|ui(?=^|$|')/<m>/"
  - "xform/'|<|>//"
"紫光双拼":
  - "derive/^([jqxy])u(?=^|$|')/$1v/"
  - "derive/'([jqxy])u(?=^|$|')/'$1v/"
  - "xform/'([aoe].*)(?=^|$|')/'<o>$1/"
  - "xform/^([aoe].*)(?=^|$|')/<o>$1/"
  - "xform/en(?=^|$|')/<w>/"
  - "xform/eng(?=^|$|')/<t>/"
  - "xform/in(?=^|$|')|uai(?=^|$|')/<y>/"
  - "xform/^zh/<u>/"
  - "xform/^sh/<i>/"
  - "xform/'zh/'<u>/"
  - "xform/'sh/'<i>/"
  - "xform/uo(?=^|$|')/<o>/"
  - "xform/ai(?=^|$|')/<p>/"
  - "xform/^ch/<a>/"
  - "xform/'ch/'<a>/"
  - "xform/[iu]ang(?=^|$|')/<g>/"
  - "xform/ang(?=^|$|')/<s>/"
  - "xform/ie(?=^|$|')/<d>/"
  - "xform/ian(?=^|$|')/<f>/"
  - "xform/([a-z>])i?ong(?=^|$|')/$1<h>/"
  - "xform/er(?=^|$|')|iu(?=^|$|')/<j>/"
  - "xform/ei(?=^|$|')/<k>/"
  - "xform/uan(?=^|$|')/<l>/"
  - "xform/ing(?=^|$|')/;/"
  - "xform/ou(?=^|$|')/<z>/"
  - "xform/[iu]a(?=^|$|')/<x>/"
  - "xform/iao(?=^|$|')/<b>/"
  - "xform/ue(?=^|$|')|ui(?=^|$|')|ve(?=^|$|')/<n>/"
  - "xform/un(?=^|$|')/<m>/"
  - "xform/ao(?=^|$|')/<q>/"
  - "xform/an(?=^|$|')/<r>/"
  - "xform/'|<|>//"
"自然码":
  - "derive/^([jqxy])u(?=^|$|')/$1v/"
  - "derive/'([jqxy])u(?=^|$|')/'$1v/"
  - "derive/^([aoe])([ioun])(?=^|$|')/$1$1$2/"
  - "derive/'([aoe])([ioun])(?=^|$|')/'$1$1$2/"
  - "xform/^([aoe])(ng)?(?=^|$|')/$1$1$2/"
  - "xform/'([aoe])(ng)?(?=^|$|')/'$1$1$2/"
  - "xform/iu(?=^|$|')/<q>/"
  - "xform/[iu]a(?=^|$|')/<w>/"
  - "xform/[uv]an(?=^|$|')/<r>/"
  - "xform/[uv]e(?=^|$|')/<t>/"
  - "xform/ing(?=^|$|')|uai(?=^|$|')/<y>/"
  - "xform/^sh/<u>/"
  - "xform/^ch/<i>/"
  - "xform/^zh/<v>/"
  - "xform/'sh/'<u>/"
  - "xform/'ch/'<i>/"
  - "xform/'zh/'<v>/"
  - "xform/uo(?=^|$|')/<o>/"
  - "xform/[uv]n(?=^|$|')/<p>/"
  - "xform/([a-z>])i?ong(?=^|$|')/$1<s>/"
  - "xform/[iu]ang(?=^|$|')/<d>/"
  - "xform/([a-z>])en(?=^|$|')/$1<f>/"
  - "xform/([a-z>])eng(?=^|$|')/$1<g>/"
  - "xform/([a-z>])ang(?=^|$|')/$1<h>/"
  - "xform/ian(?=^|$|')/<m>/"
  - "xform/([a-z>])an(?=^|$|')/$1<j>/"
  - "xform/iao(?=^|$|')/<c>/"
  - "xform/([a-z>])ao(?=^|$|')/$1<k>/"
  - "xform/([a-z>])ai(?=^|$|')/$1<l>/"
  - "xform/([a-z>])ei(?=^|$|')/$1<z>/"
  - "xform/ie(?=^|$|')/<x>/"
  - "xform/ui(?=^|$|')/<v>/"
  - "xform/([a-z>])ou(?=^|$|')/$1<b>/"
  - "xform/in(?=^|$|')/<n>/"
  - "xform/'|<|>//"